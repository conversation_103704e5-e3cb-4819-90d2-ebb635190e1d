<?php
require_once 'config/config.php';

// Lista de produtos com seus ícones e nomes formatados
$produtos = [
    'CREDITO_PESSOAL' => [
        'nome' => 'Crédito Pessoal',
        'icone' => 'fa-money-bill-wave',
        'cor' => '#00AE9D'
    ],
    'CHEQUE_ESPECIAL' => [
        'nome' => 'Cheque Especial',
        'icone' => 'fa-money-check',
        'cor' => '#003641'
    ],
    'CARTAO_CREDITO' => [
        'nome' => 'Cartão de Crédito',
        'icone' => 'fa-credit-card',
        'cor' => '#C9D200'
    ],
    'DEBITO_AUTOMATICO' => [
        'nome' => 'Débito Automático',
        'icone' => 'fa-sync',
        'cor' => '#7DB61C'
    ],
    'TOTAL' => [
        'nome' => 'Ranking Geral',
        'icone' => 'fa-trophy',
        'cor' => '#C9D200'
    ]
];

// Busca todos os desafios
$stmt = $pdo->query("SELECT id, nome, status, data_inicio, data_fim FROM desafios ORDER BY data_inicio DESC");
$desafios = $stmt->fetchAll();

// Obtém o desafio selecionado (default para o primeiro desafio)
$desafio_id = isset($_GET['desafio']) ? (int)$_GET['desafio'] : ($desafios[0]['id'] ?? null);

// Busca informações completas do desafio atual
$stmt = $pdo->prepare("SELECT id, nome, status, data_inicio, data_fim, tipo_ranking FROM desafios WHERE id = ?");
$stmt->execute([$desafio_id]);
$desafio_atual = $stmt->fetch();

if (!$desafio_atual) {
    die('Desafio não encontrado');
}

$tipo_ranking = $desafio_atual['tipo_ranking'] ?? 'vendas';

// Se for ranking por vendas, remove o ranking geral
if ($tipo_ranking === 'vendas') {
    unset($produtos['TOTAL']);
}
// Se for ranking por pontos, mantém apenas o ranking geral
if ($tipo_ranking === 'pontos') {
    $produtos = ['TOTAL' => $produtos['TOTAL']];
}

// Inicializa os arrays de rankings
$rankings_usuarios = [];
$rankings_pas = [];

if ($tipo_ranking === 'pontos') {
    // Query para ranking geral por pontos
    $query = "
        SELECT 
            v.usuario as nome,
            SUM(COALESCE(dp.pontos, 0)) as total_pontos,
            COUNT(*) as total_vendas
        FROM campanha_vendas v
        LEFT JOIN desafio_pontuacoes dp ON dp.desafio_id = v.desafio_id AND dp.produto = v.produto
        WHERE v.desafio_id = ?
        GROUP BY v.usuario
        ORDER BY total_pontos DESC";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$desafio_id]);
    $rankings_usuarios['TOTAL'] = $stmt->fetchAll();

    $query = "
        SELECT 
            v.pa as nome,
            SUM(COALESCE(dp.pontos, 0)) as total_pontos,
            COUNT(*) as total_vendas
        FROM campanha_vendas v
        LEFT JOIN desafio_pontuacoes dp ON dp.desafio_id = v.desafio_id AND dp.produto = v.produto
        WHERE v.desafio_id = ?
        GROUP BY v.pa
        ORDER BY total_pontos DESC";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$desafio_id]);
    $rankings_pas['TOTAL'] = $stmt->fetchAll();
} else {
    // Query para ranking por produto (vendas)
    foreach ($produtos as $codigo => $produto) {
        $query = "
            SELECT 
                v.usuario as nome,
                v.produto,
                SUM(v.valor) as valor_total,
                COUNT(*) as total_vendas
            FROM campanha_vendas v
            WHERE v.desafio_id = ? AND v.produto = ?
            GROUP BY v.usuario
            ORDER BY valor_total DESC
            LIMIT 5";

        $stmt = $pdo->prepare($query);
        $stmt->execute([$desafio_id, $codigo]);
        $rankings_usuarios[$codigo] = $stmt->fetchAll();

        $query = "
            SELECT 
                v.pa as nome,
                v.produto,
                SUM(v.valor) as valor_total,
                COUNT(*) as total_vendas
            FROM campanha_vendas v
            WHERE v.desafio_id = ? AND v.produto = ?
            GROUP BY v.pa
            ORDER BY valor_total DESC
            LIMIT 5";

        $stmt = $pdo->prepare($query);
        $stmt->execute([$desafio_id, $codigo]);
        $rankings_pas[$codigo] = $stmt->fetchAll();
    }
}

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ranking - Desafio RPA</title>
    <link rel="icon" type="image/png" href="assets/images/icon.png">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/ranking.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="ranking-page">
    <header>
        <div class="container">
            <img src="assets/images/logo1.png" alt="Sicoob" class="logo">
            <div class="header-content">
                <h1>RANKING - <?php echo htmlspecialchars($desafio_atual['nome']); ?></h1>
                <div class="desafio-selector">
                    <form method="GET" id="desafioForm">
                        <select name="desafio" id="desafio" onchange="this.form.submit()">
                            <?php foreach ($desafios as $desafio): ?>
                                <option value="<?php echo $desafio['id']; ?>" 
                                        <?php echo $desafio['id'] == $desafio_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($desafio['nome']); ?> 
                                    (<?php echo date('d/m/Y', strtotime($desafio['data_inicio'])); ?> - 
                                     <?php echo date('d/m/Y', strtotime($desafio['data_fim'])); ?>)
                                    <?php echo $desafio['status'] === 'ativo' ? ' - Ativo' : ''; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="dashboard">
            <?php foreach ($produtos as $codigo => $produto): ?>
                <div class="dashboard-card">
                    <div class="card-header" style="border-color: <?php echo $produto['cor']; ?>">
                        <i class="fas <?php echo $produto['icone']; ?>" style="color: <?php echo $produto['cor']; ?>"></i>
                        <h2><?php echo $produto['nome']; ?></h2>
                    </div>

                    <div class="card-content">
                        <div class="ranking-tabs">
                            <button class="tab-button active" onclick="showTab(this, 'pa-<?php echo $codigo; ?>')">
                                <i class="fas fa-building"></i> Por PA
                            </button>
                            <button class="tab-button" onclick="showTab(this, 'user-<?php echo $codigo; ?>')">
                                <i class="fas fa-users"></i> Por Usuário
                            </button>
                            <div class="export-buttons">
                                <a href="exportar_ranking.php?desafio=<?php echo $desafio_id; ?>&tipo=pas&produto=<?php echo $codigo; ?>" 
                                   class="btn btn-export" title="Exportar Ranking de PAs">
                                    <i class="fas fa-building"></i>
                                    <i class="fas fa-file-excel"></i>
                                </a>
                                <a href="exportar_ranking.php?desafio=<?php echo $desafio_id; ?>&tipo=usuarios&produto=<?php echo $codigo; ?>" 
                                   class="btn btn-export" title="Exportar Ranking de Usuários">
                                    <i class="fas fa-users"></i>
                                    <i class="fas fa-file-excel"></i>
                                </a>
                            </div>
                        </div>

                        <div class="tab-content active" id="pa-<?php echo $codigo; ?>">
                            <div class="chart-container">
                                <canvas id="chart-pa-<?php echo $codigo; ?>"></canvas>
                            </div>
                            <div class="ranking-list">
                                <?php if (isset($rankings_pas[$codigo]) && !empty($rankings_pas[$codigo])): ?>
                                    <?php 
                                    // Se for ranking por pontos, mostra todas as PAs
                                    $items = $tipo_ranking === 'pontos' ? $rankings_pas[$codigo] : array_slice($rankings_pas[$codigo], 0, 5);
                                    
                                    // Calcula os totais
                                    $total_pontos = 0;
                                    $total_vendas = 0;
                                    foreach ($rankings_pas[$codigo] as $ranking) {
                                        $total_pontos += $ranking['total_pontos'] ?? $ranking['valor_total'] ?? 0;
                                        $total_vendas += $ranking['total_vendas'];
                                    }

                                    foreach ($items as $pos => $ranking): 
                                    ?>
                                        <div class="ranking-item" style="--rank-color: <?php echo $produto['cor']; ?>">
                                            <span class="position"><?php echo $pos + 1; ?>º</span>
                                            <span class="name">PA <?php echo $ranking['nome']; ?></span>
                                            <span class="value">
                                                <?php 
                                                if ($tipo_ranking === 'pontos') {
                                                    echo number_format($ranking['total_pontos']) . ' pontos';
                                                } else if ($codigo === 'DEBITO_AUTOMATICO') {
                                                    echo $ranking['total_vendas'] . ' ativações';
                                                } else {
                                                    echo 'R$ ' . number_format($ranking['valor_total'], 2, ',', '.');
                                                }
                                                ?>
                                            </span>
                                            <span class="quantity"><?php echo $ranking['total_vendas']; ?> vendas</span>
                                        </div>
                                    <?php endforeach; ?>

                                    <div class="ranking-total" style="--rank-color: <?php echo $produto['cor']; ?>">
                                        <span class="total-label">Total Geral:</span>
                                        <div class="total-info">
                                            <span class="total-value">
                                                <?php 
                                                if ($tipo_ranking === 'pontos') {
                                                    echo number_format($total_pontos) . ' pontos';
                                                } else if ($codigo === 'DEBITO_AUTOMATICO') {
                                                    echo $total_vendas . ' ativações';
                                                } else {
                                                    echo 'R$ ' . number_format($total_pontos, 2, ',', '.');
                                                }
                                                ?>
                                            </span>
                                            <span class="total-separator">/</span>
                                            <span class="total-quantity"><?php echo number_format($total_vendas); ?> vendas</span>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <p class="no-data">Nenhuma venda registrada</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="tab-content" id="user-<?php echo $codigo; ?>">
                            <div class="chart-container">
                                <canvas id="chart-user-<?php echo $codigo; ?>"></canvas>
                            </div>
                            <div class="ranking-list">
                                <?php if (isset($rankings_usuarios[$codigo]) && !empty($rankings_usuarios[$codigo])): ?>
                                    <?php 
                                    // Se for ranking por pontos, mostra todos os usuários
                                    $items = $tipo_ranking === 'pontos' ? $rankings_usuarios[$codigo] : array_slice($rankings_usuarios[$codigo], 0, 5);
                                    
                                    // Calcula os totais
                                    $total_pontos = 0;
                                    $total_vendas = 0;
                                    foreach ($rankings_usuarios[$codigo] as $ranking) {
                                        $total_pontos += $ranking['total_pontos'] ?? $ranking['valor_total'] ?? 0;
                                        $total_vendas += $ranking['total_vendas'];
                                    }

                                    foreach ($items as $pos => $ranking): 
                                    ?>
                                        <div class="ranking-item" style="--rank-color: <?php echo $produto['cor']; ?>">
                                            <span class="position"><?php echo $pos + 1; ?>º</span>
                                            <span class="name"><?php echo $ranking['nome']; ?></span>
                                            <span class="value">
                                                <?php 
                                                if ($tipo_ranking === 'pontos') {
                                                    echo number_format($ranking['total_pontos']) . ' pontos';
                                                } else if ($codigo === 'DEBITO_AUTOMATICO') {
                                                    echo $ranking['total_vendas'] . ' ativações';
                                                } else {
                                                    echo 'R$ ' . number_format($ranking['valor_total'], 2, ',', '.');
                                                }
                                                ?>
                                            </span>
                                            <span class="quantity"><?php echo $ranking['total_vendas']; ?> vendas</span>
                                        </div>
                                    <?php endforeach; ?>

                                    <div class="ranking-total" style="--rank-color: <?php echo $produto['cor']; ?>">
                                        <span class="total-label">Total Geral:</span>
                                        <div class="total-info">
                                            <span class="total-value">
                                                <?php 
                                                if ($tipo_ranking === 'pontos') {
                                                    echo number_format($total_pontos) . ' pontos';
                                                } else if ($codigo === 'DEBITO_AUTOMATICO') {
                                                    echo $total_vendas . ' ativações';
                                                } else {
                                                    echo 'R$ ' . number_format($total_pontos, 2, ',', '.');
                                                }
                                                ?>
                                            </span>
                                            <span class="total-separator">/</span>
                                            <span class="total-quantity"><?php echo number_format($total_vendas); ?> vendas</span>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <p class="no-data">Nenhuma venda registrada</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </main>

    <style>
    .ranking-page {
        background: var(--sicoob-gray-100);
        min-height: 100vh;
    }

    header {
        background: var(--sicoob-dark-green);
        padding: 1.5rem 0;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
    }

    header .container {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    header h1 {
        color: var(--sicoob-light-green);
        margin: 0;
        font-size: 2rem;
    }

    .logo {
        height: 50px;
        width: auto;
    }

    .dashboard {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .dashboard-card {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
    }

    .card-header {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        border-bottom: 3px solid;
    }

    .card-header i {
        font-size: 1.5rem;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
        color: var(--sicoob-dark-green);
    }

    .card-content {
        padding: 1.5rem;
    }

    .ranking-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: center;
        justify-content: space-between;
    }

    .tab-button {
        padding: 0.75rem 1.5rem;
        border: none;
        background: var(--sicoob-gray-200);
        color: var(--sicoob-gray-700);
        border-radius: var(--radius-md);
        cursor: pointer;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
    }

    .tab-button.active {
        background: var(--sicoob-turquoise);
        color: white;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .chart-container {
        margin-bottom: 1.5rem;
        height: 200px;
    }

    .ranking-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .ranking-item {
        display: grid;
        grid-template-columns: auto 1fr auto auto;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem;
        background: var(--sicoob-gray-100);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        transition: transform 0.2s ease;
    }

    .ranking-item:hover {
        transform: translateX(5px);
    }

    .position {
        font-weight: 700;
        color: var(--rank-color);
        width: 2rem;
    }

    .name {
        font-weight: 500;
    }

    .value {
        font-family: monospace;
        font-weight: 500;
        color: var(--sicoob-dark-green);
    }

    .quantity {
        color: var(--sicoob-gray-600);
        font-size: 0.75rem;
    }

    .ranking-total {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: var(--sicoob-gray-200);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        margin-top: 1rem;
        border-left: 4px solid var(--rank-color);
    }

    .total-label {
        font-weight: 700;
        color: var(--sicoob-dark-green);
    }

    .total-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .total-value {
        font-family: monospace;
        font-weight: 700;
        color: var(--rank-color);
        font-size: 1rem;
    }

    .total-separator {
        color: var(--sicoob-gray-400);
        font-weight: 500;
    }

    .total-quantity {
        color: var(--sicoob-gray-700);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .no-data {
        text-align: center;
        color: var(--sicoob-gray-500);
        padding: 2rem;
    }

    @media (max-width: 768px) {
        .dashboard {
            grid-template-columns: 1fr;
        }

        header .container {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        header h1 {
            font-size: 1.5rem;
        }

        .ranking-item {
            grid-template-columns: auto 1fr;
            grid-template-rows: auto auto;
        }

        .value, .quantity {
            grid-column: 1 / -1;
            text-align: right;
        }

        .ranking-tabs {
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .export-buttons {
            width: 100%;
            justify-content: flex-end;
            margin-top: 0.5rem;
        }
    }

    .header-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .desafio-selector {
        max-width: 600px;
    }

    .desafio-selector select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--sicoob-light-green);
        border-radius: var(--radius-sm);
        background-color: var(--sicoob-dark-green);
        color: var(--sicoob-light-green);
        font-size: 1rem;
    }

    .desafio-selector select:focus {
        outline: none;
        border-color: var(--sicoob-turquoise);
    }

    .desafio-selector select option {
        background-color: white;
        color: var(--sicoob-dark-green);
    }

    .export-buttons {
        display: flex;
        gap: 0.5rem;
        margin-left: auto;
    }

    .btn-export {
        padding: 0.5rem;
        border: none;
        background: var(--sicoob-turquoise);
        color: white;
        border-radius: var(--radius-md);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
        width: auto;
        min-width: 36px;
        height: 36px;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .btn-export:hover {
        background: var(--sicoob-dark-green);
        transform: translateY(-2px);
    }

    .btn-export i {
        font-size: 0.875rem;
    }

    .btn-export i:first-child {
        margin-right: -0.125rem;
    }

    .btn-export i:last-child {
        font-size: 0.75rem;
        margin-top: 0.125rem;
    }
    </style>

    <script>
    function showTab(button, tabId) {
        // Remove active class from all buttons and tabs in the same card
        const card = button.closest('.dashboard-card');
        card.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        card.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));

        // Add active class to clicked button and corresponding tab
        button.classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    // Inicializa os gráficos
    <?php foreach ($produtos as $codigo => $produto): ?>
        // Dados para o gráfico de PA
        <?php if (isset($rankings_pas[$codigo]) && !empty($rankings_pas[$codigo])): ?>
            new Chart(document.getElementById('chart-pa-<?php echo $codigo; ?>'), {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode(array_map(function($ranking) { return 'PA ' . $ranking['nome']; }, array_slice($rankings_pas[$codigo], 0, 5))); ?>,
                    datasets: [{
                        label: <?php echo $tipo_ranking === 'pontos' ? "'Pontos'" : "'Valor Total'"; ?>,
                        data: <?php 
                            if ($tipo_ranking === 'pontos') {
                                echo json_encode(array_map(function($ranking) { return $ranking['total_pontos']; }, array_slice($rankings_pas[$codigo], 0, 5)));
                            } else {
                                echo json_encode(array_map(function($ranking) { return $ranking['valor_total']; }, array_slice($rankings_pas[$codigo], 0, 5)));
                            }
                        ?>,
                        backgroundColor: '<?php echo $produto['cor']; ?>',
                        borderColor: '<?php echo $produto['cor']; ?>',
                        borderWidth: 1,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: <?php echo $tipo_ranking === 'pontos' ? "'y'" : "'x'"; ?>,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    <?php if ($tipo_ranking === 'pontos'): ?>
                                    return context.raw.toLocaleString('pt-BR') + ' pontos';
                                    <?php else: ?>
                                    return 'R$ ' + context.raw.toLocaleString('pt-BR');
                                    <?php endif; ?>
                                }
                            }
                        }
                    },
                    scales: {
                        <?php if ($tipo_ranking === 'pontos'): ?>
                        x: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('pt-BR') + ' pts';
                                }
                            }
                        },
                        y: {
                            ticks: {
                                font: {
                                    family: 'Source Sans Pro'
                                }
                            }
                        }
                        <?php else: ?>
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toLocaleString('pt-BR');
                                }
                            }
                        }
                        <?php endif; ?>
                    }
                },
                <?php if ($tipo_ranking === 'pontos'): ?>
                plugins: [{
                    afterDraw: function(chart) {
                        var ctx = chart.ctx;
                        chart.data.datasets.forEach(function(dataset, i) {
                            var meta = chart.getDatasetMeta(i);
                            if (!meta.hidden) {
                                meta.data.forEach(function(element, index) {
                                    // Desenha o texto com o valor da pontuação
                                    var value = dataset.data[index];
                                    var text = value.toLocaleString('pt-BR') + ' pts';
                                    
                                    ctx.save();
                                    ctx.textAlign = 'left';
                                    ctx.textBaseline = 'middle';
                                    ctx.font = '12px Source Sans Pro';
                                    ctx.fillStyle = '#003641';
                                    
                                    // Posição do texto depois da barra
                                    ctx.fillText(text, element.x + 5, element.y);
                                    ctx.restore();
                                });
                            }
                        });
                    }
                }]
                <?php endif; ?>
            });
        <?php endif; ?>

        // Dados para o gráfico de Usuários
        <?php if (isset($rankings_usuarios[$codigo]) && !empty($rankings_usuarios[$codigo])): ?>
            new Chart(document.getElementById('chart-user-<?php echo $codigo; ?>'), {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode(array_map(function($ranking) { return $ranking['nome']; }, array_slice($rankings_usuarios[$codigo], 0, 5))); ?>,
                    datasets: [{
                        label: <?php echo $tipo_ranking === 'pontos' ? "'Pontos'" : "'Valor Total'"; ?>,
                        data: <?php 
                            if ($tipo_ranking === 'pontos') {
                                echo json_encode(array_map(function($ranking) { return $ranking['total_pontos']; }, array_slice($rankings_usuarios[$codigo], 0, 5)));
                            } else {
                                echo json_encode(array_map(function($ranking) { return $ranking['valor_total']; }, array_slice($rankings_usuarios[$codigo], 0, 5)));
                            }
                        ?>,
                        backgroundColor: '<?php echo $produto['cor']; ?>',
                        borderColor: '<?php echo $produto['cor']; ?>',
                        borderWidth: 1,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: <?php echo $tipo_ranking === 'pontos' ? "'y'" : "'x'"; ?>,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    <?php if ($tipo_ranking === 'pontos'): ?>
                                    return context.raw.toLocaleString('pt-BR') + ' pontos';
                                    <?php else: ?>
                                    return 'R$ ' + context.raw.toLocaleString('pt-BR');
                                    <?php endif; ?>
                                }
                            }
                        }
                    },
                    scales: {
                        <?php if ($tipo_ranking === 'pontos'): ?>
                        x: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('pt-BR') + ' pts';
                                }
                            }
                        },
                        y: {
                            ticks: {
                                font: {
                                    family: 'Source Sans Pro'
                                }
                            }
                        }
                        <?php else: ?>
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toLocaleString('pt-BR');
                                }
                            }
                        }
                        <?php endif; ?>
                    }
                },
                <?php if ($tipo_ranking === 'pontos'): ?>
                plugins: [{
                    afterDraw: function(chart) {
                        var ctx = chart.ctx;
                        chart.data.datasets.forEach(function(dataset, i) {
                            var meta = chart.getDatasetMeta(i);
                            if (!meta.hidden) {
                                meta.data.forEach(function(element, index) {
                                    // Desenha o texto com o valor da pontuação
                                    var value = dataset.data[index];
                                    var text = value.toLocaleString('pt-BR') + ' pts';
                                    
                                    ctx.save();
                                    ctx.textAlign = 'left';
                                    ctx.textBaseline = 'middle';
                                    ctx.font = '12px Source Sans Pro';
                                    ctx.fillStyle = '#003641';
                                    
                                    // Posição do texto depois da barra
                                    ctx.fillText(text, element.x + 5, element.y);
                                    ctx.restore();
                                });
                            }
                        });
                    }
                }]
                <?php endif; ?>
            });
        <?php endif; ?>
    <?php endforeach; ?>

    // Auto refresh a cada 5 minutos
    setTimeout(function() {
        window.location.reload();
    }, 300000);
    </script>
</body>
</html> 