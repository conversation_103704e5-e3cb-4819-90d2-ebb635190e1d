<?php
session_start();
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Define o fuso horário para Brasil
date_default_timezone_set('America/Sao_Paulo');

// Verifica se o usuário está autenticado e é administrador
check_auth();
if (!$_SESSION['is_admin']) {
    header('Location: ../index.php');
    exit();
}

// Função para verificar e finalizar desafios automaticamente
function verificar_desafios_auto_finalizacao($pdo) {
    $agora = new DateTime();
    
    $stmt = $pdo->prepare("
        UPDATE desafios 
        SET status = 'finalizado' 
        WHERE status = 'ativo' 
        AND auto_finalizar = 1 
        AND (
            data_fim < CURRENT_DATE
            OR (
                data_fim = CURRENT_DATE 
                AND horario_fim <= TIME(NOW())
            )
        )
    ");
    
    $stmt->execute();
    return $stmt->rowCount();
}

// Verificar desafios que precisam ser finalizados
verificar_desafios_auto_finalizacao($pdo);

// Processar ações POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            $pdo->beginTransaction();

            switch ($_POST['action']) {
                case 'criar':
                    // Verifica se já existe algum desafio ativo quando tenta criar um ativo
                    if ($_POST['status'] === 'ativo') {
                        $stmt = $pdo->query("SELECT COUNT(*) FROM desafios WHERE status = 'ativo'");
                        $desafios_ativos = $stmt->fetchColumn();

                        if ($desafios_ativos > 0) {
                            throw new Exception("Já existe um desafio ativo. Finalize o desafio atual antes de ativar outro.");
                        }
                    }

                    $stmt = $pdo->prepare("
                        INSERT INTO desafios (
                            nome, descricao, data_inicio, data_fim, status, 
                            meta_vendas, tipo_ranking, regras, auto_finalizar, 
                            horario_fim
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $auto_finalizar = isset($_POST['auto_finalizar']) ? 1 : 0;
                    $horario_fim = $_POST['horario_fim'] ?? '23:59:59';
                    
                    $stmt->execute([
                        $_POST['nome'],
                        $_POST['descricao'] ?? null,
                        $_POST['data_inicio'],
                        $_POST['data_fim'],
                        $_POST['status'],
                        $_POST['meta_vendas'] ?: null,
                        $_POST['tipo_ranking'],
                        $_POST['regras'],
                        $auto_finalizar,
                        $horario_fim
                    ]);

                    if ($_POST['tipo_ranking'] === 'pontos') {
                        $desafio_id = $pdo->lastInsertId();
                        $stmt = $pdo->prepare("INSERT INTO desafio_pontuacoes (desafio_id, produto, pontos) VALUES (?, ?, ?)");
                        
                        foreach ($_POST['pontos'] as $produto => $pontos) {
                            $stmt->execute([$desafio_id, $produto, $pontos]);
                        }
                    }

                    $_SESSION['success_message'] = "Desafio criado com sucesso!";
                    break;

                case 'atualizar':
                    // Verifica se já existe algum desafio ativo quando tenta atualizar para ativo
                    if ($_POST['status'] === 'ativo') {
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM desafios WHERE status = 'ativo' AND id != ?");
                        $stmt->execute([$_POST['id']]);
                        $desafios_ativos = $stmt->fetchColumn();

                        if ($desafios_ativos > 0) {
                            throw new Exception("Já existe um desafio ativo. Finalize o desafio atual antes de ativar outro.");
                        }
                    }

                    $stmt = $pdo->prepare("
                        UPDATE desafios 
                        SET nome = ?, descricao = ?, data_inicio = ?, data_fim = ?, 
                            status = ?, meta_vendas = ?, tipo_ranking = ?, regras = ?,
                            auto_finalizar = ?, horario_fim = ?
                        WHERE id = ?
                    ");
                    
                    $auto_finalizar = isset($_POST['auto_finalizar']) ? 1 : 0;
                    $horario_fim = $_POST['horario_fim'] ?? '23:59:59';
                    
                    $stmt->execute([
                        $_POST['nome'],
                        $_POST['descricao'] ?? null,
                        $_POST['data_inicio'],
                        $_POST['data_fim'],
                        $_POST['status'],
                        $_POST['meta_vendas'] ?: null,
                        $_POST['tipo_ranking'],
                        $_POST['regras'],
                        $auto_finalizar,
                        $horario_fim,
                        $_POST['id']
                    ]);

                    if ($_POST['tipo_ranking'] === 'pontos') {
                        // Remover pontuações antigas
                        $stmt = $pdo->prepare("DELETE FROM desafio_pontuacoes WHERE desafio_id = ?");
                        $stmt->execute([$_POST['id']]);

                        // Inserir novas pontuações
                        $stmt = $pdo->prepare("INSERT INTO desafio_pontuacoes (desafio_id, produto, pontos) VALUES (?, ?, ?)");
                        foreach ($_POST['pontos'] as $produto => $pontos) {
                            $stmt->execute([$_POST['id'], $produto, $pontos]);
                        }
                    }

                    $_SESSION['success_message'] = "Desafio atualizado com sucesso!";
                    break;

                case 'ativar':
                    // Verifica se já existe algum desafio ativo
                    $stmt = $pdo->query("SELECT COUNT(*) FROM desafios WHERE status = 'ativo' AND id != " . $_POST['id']);
                    $desafios_ativos = $stmt->fetchColumn();

                    if ($desafios_ativos > 0) {
                        throw new Exception("Já existe um desafio ativo. Finalize o desafio atual antes de ativar outro.");
                    }

                    // Verifica se o desafio está dentro do período válido
                    $stmt = $pdo->prepare("SELECT data_inicio, data_fim, horario_fim FROM desafios WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $datas = $stmt->fetch();
                    
                    $hoje = new DateTime();
                    $inicio = new DateTime($datas['data_inicio']);
                    $fim = new DateTime($datas['data_fim'] . ' ' . ($datas['horario_fim'] ?? '23:59:59'));

                    if ($hoje < $inicio) {
                        throw new Exception("Este desafio ainda não pode ser ativado pois sua data de início é " . $inicio->format('d/m/Y'));
                    }
                    
                    if ($hoje > $fim) {
                        throw new Exception(
                            "Este desafio não pode ser ativado pois já passou do prazo final (" . 
                            $fim->format('d/m/Y H:i') . "). " .
                            "Para ativá-lo, primeiro edite o desafio e ajuste a data/hora de término."
                        );
                    }

                    // Ativa o desafio selecionado
                    $stmt = $pdo->prepare("UPDATE desafios SET status = 'ativo' WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    
                    $_SESSION['success_message'] = "Desafio ativado com sucesso!";
                    break;

                case 'finalizar':
                    $stmt = $pdo->prepare("UPDATE desafios SET status = 'finalizado' WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $_SESSION['success_message'] = "Desafio finalizado com sucesso!";
                    break;

                case 'excluir':
                    // Primeiro exclui as pontuações do desafio
                    $stmt = $pdo->prepare("DELETE FROM desafio_pontuacoes WHERE desafio_id = ?");
                    $stmt->execute([$_POST['id']]);

                    // Depois exclui os registros da campanha_vendas
                    $stmt = $pdo->prepare("DELETE FROM campanha_vendas WHERE desafio_id = ?");
                    $stmt->execute([$_POST['id']]);

                    // Depois exclui as vendas do desafio
                    $stmt = $pdo->prepare("DELETE FROM vendas WHERE desafio_id = ?");
                    $stmt->execute([$_POST['id']]);

                    // Por fim, exclui o desafio
                    $stmt = $pdo->prepare("DELETE FROM desafios WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    
                    $_SESSION['success_message'] = "Desafio excluído com sucesso!";
                    break;
            }

            $pdo->commit();
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        $_SESSION['error_message'] = "Erro: " . $e->getMessage();
    }
    
    header('Location: desafios.php');
    exit();
}

// Buscar todos os desafios
$desafios = $pdo->query("SELECT * FROM desafios ORDER BY created_at DESC")->fetchAll();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Desafios - Campanha Sicoob</title>
    <link rel="icon" type="image/png" href="../assets/images/icon.png">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/table.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    /* Garantir que os links do menu não fiquem em negrito */
    nav ul li a {
        font-weight: normal !important;
    }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <img src="../assets/images/logo1.png" alt="Sicoob" class="logo">
            <?php include '../includes/menu.php'; ?>
        </div>
    </header>

    <main class="container">
        <h1>Gestão de Desafios</h1>
        
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="success-message">
                <?php 
                echo $_SESSION['success_message'];
                unset($_SESSION['success_message']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="error-message">
                <?php 
                echo $_SESSION['error_message'];
                unset($_SESSION['error_message']);
                ?>
            </div>
        <?php endif; ?>

        <div class="content">
            <div class="table-container">
                <div class="table-header">
                    <h2><i class="fas fa-trophy"></i> Desafios Cadastrados</h2>
                    <div class="table-toolbar">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#novoDesafioModal">
                            <i class="fas fa-plus"></i> Criar Novo Desafio
                        </button>
                        <span class="total-records">
                            <i class="fas fa-chart-bar"></i>
                            Total: <?php echo count($desafios); ?> desafios
                        </span>
                    </div>
                </div>

                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Período</th>
                                <th>Status</th>
                                <th>Meta</th>
                                <th>Tipo</th>
                                <th>Finalização</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($desafios as $desafio): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($desafio['nome']); ?></td>
                                <td>
                                    <?php 
                                    echo date('d/m/Y', strtotime($desafio['data_inicio'])) . ' até ' . 
                                         date('d/m/Y', strtotime($desafio['data_fim']));
                                    ?>
                                </td>
                                <td>
                                    <?php 
                                    $status_class = '';
                                    $status_icon = '';
                                    switch($desafio['status']) {
                                        case 'ativo':
                                            $status_class = 'badge badge-success';
                                            $status_icon = '<i class="fas fa-check-circle"></i>';
                                            break;
                                        case 'inativo':
                                            $status_class = 'badge badge-secondary';
                                            $status_icon = '<i class="fas fa-pause-circle"></i>';
                                            break;
                                        case 'finalizado':
                                            $status_class = 'badge badge-info';
                                            $status_icon = '<i class="fas fa-flag-checkered"></i>';
                                            break;
                                    }
                                    echo "<span class='{$status_class}' style='font-size: 0.9em; padding: 8px 12px;'>{$status_icon} " . ucfirst(htmlspecialchars($desafio['status'])) . "</span>";
                                    ?>
                                </td>
                                <td>R$ <?php echo number_format($desafio['meta_vendas'], 2, ',', '.'); ?></td>
                                <td>
                                    <?php 
                                    $tipo = $desafio['tipo_ranking'] ?? 'vendas';
                                    echo $tipo === 'pontos' ? 'Por Pontuação' : 'Por Vendas';
                                    ?>
                                </td>
                                <td>
                                    <?php if ($desafio['auto_finalizar']): ?>
                                        <span class="badge badge-warning">
                                            <i class="fas fa-clock"></i> Auto (<?php echo substr($desafio['horario_fim'], 0, 5); ?>)
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-user"></i> Manual
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="actions-column">
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-icon btn-primary" onclick="editarDesafio(<?php echo htmlspecialchars(json_encode($desafio)); ?>)" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        
                                        <?php if ($desafio['status'] !== 'ativo'): ?>
                                        <form method="post" class="d-inline" onsubmit="return confirm('Tem certeza que deseja ativar este desafio?');">
                                            <input type="hidden" name="action" value="ativar">
                                            <input type="hidden" name="id" value="<?php echo $desafio['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-icon btn-success" title="Ativar">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>

                                        <?php if ($desafio['status'] !== 'finalizado'): ?>
                                        <form method="post" class="d-inline" onsubmit="return confirm('Tem certeza que deseja finalizar este desafio?');">
                                            <input type="hidden" name="action" value="finalizar">
                                            <input type="hidden" name="id" value="<?php echo $desafio['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-icon btn-warning" title="Finalizar">
                                                <i class="fas fa-flag-checkered"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>

                                        <form method="post" class="d-inline" onsubmit="return confirm('ATENÇÃO: Esta ação excluirá permanentemente o desafio e todas as vendas associadas a ele. Tem certeza que deseja continuar?');">
                                            <input type="hidden" name="action" value="excluir">
                                            <input type="hidden" name="id" value="<?php echo $desafio['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-icon btn-danger" title="Excluir">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal para Novo Desafio -->
    <div class="modal fade" id="novoDesafioModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus-circle"></i> Novo Desafio</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="criar">
                        
                        <div class="form-group">
                            <label>Nome do Desafio</label>
                            <input type="text" class="form-control" name="nome" required>
                        </div>

                        <div class="form-group">
                            <label>Descrição</label>
                            <textarea class="form-control" name="descricao" rows="3"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label>Data Início</label>
                                <input type="date" class="form-control" name="data_inicio" required>
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label>Data Fim</label>
                                <input type="date" class="form-control" name="data_fim" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label>Status</label>
                                <select class="form-control" name="status" required>
                                    <option value="inativo">Inativo</option>
                                    <option value="ativo">Ativo</option>
                                </select>
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label>Meta de Vendas</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">R$</span>
                                    </div>
                                    <input type="number" step="0.01" class="form-control" name="meta_vendas">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Tipo de Ranking</label>
                            <select class="form-control" name="tipo_ranking" id="novoTipoRanking" onchange="togglePontuacaoNovo()" required>
                                <option value="vendas">Por Vendas</option>
                                <option value="pontos">Por Pontuação</option>
                            </select>
                        </div>

                        <div id="novoPontuacaoSection" style="display: none;">
                            <h4 class="mt-4 mb-3">Pontuação por Produto</h4>
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label>Débito Automático</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control pontos-input-novo" name="pontos[DEBITO_AUTOMATICO]" value="1">
                                        <div class="input-group-append">
                                            <span class="input-group-text">pts</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group col-md-6">
                                    <label>Crédito Pessoal</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control pontos-input-novo" name="pontos[CREDITO_PESSOAL]" value="10">
                                        <div class="input-group-append">
                                            <span class="input-group-text">pts</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label>Cheque Especial</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control pontos-input-novo" name="pontos[CHEQUE_ESPECIAL]" value="5">
                                        <div class="input-group-append">
                                            <span class="input-group-text">pts</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group col-md-6">
                                    <label>Cartão de Crédito</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control pontos-input-novo" name="pontos[CARTAO_CREDITO]" value="5">
                                        <div class="input-group-append">
                                            <span class="input-group-text">pts</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Regras</label>
                            <textarea class="form-control" name="regras" rows="3" placeholder="Descreva as regras do desafio..."></textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="autoFinalizar" name="auto_finalizar">
                                <label class="custom-control-label" for="autoFinalizar">Finalizar automaticamente</label>
                            </div>
                        </div>

                        <div class="form-group" id="horarioFimGroup" style="display: none;">
                            <label>Horário de Finalização</label>
                            <input type="time" class="form-control" name="horario_fim" id="horarioFim" value="23:59">
                            <small class="form-text text-muted">Se não especificado, o desafio será finalizado às 23:59:59 do último dia</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check"></i> Criar Desafio
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Editar Desafio -->
    <div class="modal fade" id="editarDesafioModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Editar Desafio</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="post" id="formEditarDesafio">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="atualizar">
                        <input type="hidden" name="id" id="editId">
                        
                        <div class="form-group">
                            <label>Nome do Desafio</label>
                            <input type="text" class="form-control" name="nome" id="editNome" required>
                        </div>

                        <div class="form-group">
                            <label>Descrição</label>
                            <textarea class="form-control" name="descricao" id="editDescricao" rows="3"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label>Data Início</label>
                                <input type="date" class="form-control" name="data_inicio" id="editDataInicio" required>
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label>Data Fim</label>
                                <input type="date" class="form-control" name="data_fim" id="editDataFim" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label>Status</label>
                                <select class="form-control" name="status" id="editStatus" required>
                                    <option value="inativo">Inativo</option>
                                    <option value="ativo">Ativo</option>
                                    <option value="finalizado">Finalizado</option>
                                </select>
                            </div>
                            
                            <div class="form-group col-md-6">
                                <label>Meta de Vendas</label>
                                <input type="number" step="0.01" class="form-control" name="meta_vendas" id="editMetaVendas">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Tipo de Ranking</label>
                            <select class="form-control" name="tipo_ranking" id="editTipoRanking" onchange="togglePontuacaoEdit()" required>
                                <option value="vendas">Por Vendas</option>
                                <option value="pontos">Por Pontuação</option>
                            </select>
                        </div>

                        <div id="editPontuacaoSection" style="display: none;">
                            <h4 class="mt-4 mb-3">Pontuação por Produto</h4>
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label>Débito Automático</label>
                                    <input type="number" class="form-control pontos-input" name="pontos[DEBITO_AUTOMATICO]" id="editPontosDebito">
                                </div>
                                <div class="form-group col-md-6">
                                    <label>Crédito Pessoal</label>
                                    <input type="number" class="form-control pontos-input" name="pontos[CREDITO_PESSOAL]" id="editPontosCredito">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label>Cheque Especial</label>
                                    <input type="number" class="form-control pontos-input" name="pontos[CHEQUE_ESPECIAL]" id="editPontosCheque">
                                </div>
                                <div class="form-group col-md-6">
                                    <label>Cartão de Crédito</label>
                                    <input type="number" class="form-control pontos-input" name="pontos[CARTAO_CREDITO]" id="editPontosCartao">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Regras</label>
                            <textarea class="form-control" name="regras" id="editRegras" rows="3"></textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="editAutoFinalizar" name="auto_finalizar">
                                <label class="custom-control-label" for="editAutoFinalizar">Finalizar automaticamente</label>
                            </div>
                        </div>

                        <div class="form-group" id="editHorarioFimGroup" style="display: none;">
                            <label>Horário de Finalização</label>
                            <input type="time" class="form-control" name="horario_fim" id="editHorarioFim" value="23:59">
                            <small class="form-text text-muted">Se não especificado, o desafio será finalizado às 23:59:59 do último dia</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
                        <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de Criação/Edição -->
    <div id="modal-desafio" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Novo Desafio</h3>
                <p class="modal-subtitle" id="modal-subtitle">Preencha os dados do desafio</p>
            </div>
            <form id="form-desafio" method="POST">
                <input type="hidden" name="acao" id="form-acao" value="criar">
                <input type="hidden" name="desafio_id" id="desafio-id">
                
                <div class="form-group">
                    <label for="nome">Nome do Desafio:</label>
                    <input type="text" name="nome" id="nome" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="descricao">Descrição:</label>
                    <textarea name="descricao" id="descricao" class="form-control" rows="3"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="data_inicio">Data Início:</label>
                        <input type="date" name="data_inicio" id="data_inicio" class="form-control" required>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="data_fim">Data Fim:</label>
                        <input type="date" name="data_fim" id="data_fim" class="form-control" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="meta_vendas">Meta de Vendas:</label>
                    <input type="number" name="meta_vendas" id="meta_vendas" class="form-control">
                </div>

                <div class="form-group">
                    <label for="tipo_ranking">Tipo de Ranking:</label>
                    <select name="tipo_ranking" id="tipo_ranking" class="form-control" onchange="togglePontuacao()">
                        <option value="vendas">Por Vendas</option>
                        <option value="pontos">Por Pontuação</option>
                    </select>
                </div>

                <div id="pontuacao-section" style="display: none;">
                    <h4>Pontuação por Produto</h4>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="pontos_debito">Débito Automático:</label>
                            <input type="number" name="pontos[DEBITO_AUTOMATICO]" id="pontos_debito" class="form-control pontos-input" value="1">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="pontos_credito">Crédito Pessoal:</label>
                            <input type="number" name="pontos[CREDITO_PESSOAL]" id="pontos_credito" class="form-control pontos-input" value="10">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="pontos_cheque">Cheque Especial:</label>
                            <input type="number" name="pontos[CHEQUE_ESPECIAL]" id="pontos_cheque" class="form-control pontos-input" value="5">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="pontos_cartao">Cartão de Crédito:</label>
                            <input type="number" name="pontos[CARTAO_CREDITO]" id="pontos_cartao" class="form-control pontos-input" value="5">
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" onclick="fecharModal()" class="btn btn-secondary">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts necessários -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <style>
    /* Sobrescrever estilos do Bootstrap para o menu */
    nav ul li a {
        font-weight: normal !important;
    }
    </style>

    <script>
    function togglePontuacao() {
        const tipoRanking = document.getElementById('tipo_ranking').value;
        const pontuacaoSection = document.getElementById('pontuacao-section');
        const pontosInputs = document.querySelectorAll('.pontos-input');
        
        if (tipoRanking === 'pontos') {
            pontuacaoSection.style.display = 'block';
            pontosInputs.forEach(input => input.required = true);
        } else {
            pontuacaoSection.style.display = 'none';
            pontosInputs.forEach(input => input.required = false);
        }
    }

    function togglePontuacaoEdit() {
        const tipoRanking = document.getElementById('editTipoRanking').value;
        const pontuacaoSection = document.getElementById('editPontuacaoSection');
        const pontosInputs = pontuacaoSection.querySelectorAll('.pontos-input');
        
        if (tipoRanking === 'pontos') {
            pontuacaoSection.style.display = 'block';
            pontosInputs.forEach(input => input.required = true);
        } else {
            pontuacaoSection.style.display = 'none';
            pontosInputs.forEach(input => input.required = false);
        }
    }

    function togglePontuacaoNovo() {
        const tipoRanking = document.getElementById('novoTipoRanking').value;
        const pontuacaoSection = document.getElementById('novoPontuacaoSection');
        const pontosInputs = pontuacaoSection.querySelectorAll('.pontos-input-novo');
        
        if (tipoRanking === 'pontos') {
            pontuacaoSection.style.display = 'block';
            pontosInputs.forEach(input => input.required = true);
        } else {
            pontuacaoSection.style.display = 'none';
            pontosInputs.forEach(input => input.required = false);
        }
    }

    function editarDesafio(desafio) {
        // Preencher campos básicos
        document.getElementById('editId').value = desafio.id;
        document.getElementById('editNome').value = desafio.nome;
        document.getElementById('editDescricao').value = desafio.descricao || '';
        document.getElementById('editDataInicio').value = desafio.data_inicio;
        document.getElementById('editDataFim').value = desafio.data_fim;
        document.getElementById('editStatus').value = desafio.status;
        document.getElementById('editMetaVendas').value = desafio.meta_vendas || '';
        document.getElementById('editTipoRanking').value = desafio.tipo_ranking || 'vendas';
        document.getElementById('editRegras').value = desafio.regras || '';
        
        // Preencher campos de auto finalização
        document.getElementById('editAutoFinalizar').checked = desafio.auto_finalizar == 1;
        document.getElementById('editHorarioFim').value = desafio.horario_fim || '23:59';
        document.getElementById('editHorarioFimGroup').style.display = desafio.auto_finalizar == 1 ? 'block' : 'none';

        // Mostrar/ocultar seção de pontuação com base no tipo de ranking
        const pontuacaoSection = document.getElementById('editPontuacaoSection');
        pontuacaoSection.style.display = desafio.tipo_ranking === 'pontos' ? 'block' : 'none';
        
        // Se for um desafio por pontos, buscar e preencher as pontuações
        if (desafio.tipo_ranking === 'pontos') {
            // Primeiro, definimos valores padrão
            document.getElementById('editPontosDebito').value = '1';
            document.getElementById('editPontosCredito').value = '10';
            document.getElementById('editPontosCheque').value = '5';
            document.getElementById('editPontosCartao').value = '5';

            // Depois buscamos os valores reais
            fetch(`get_pontuacoes.php?desafio_id=${desafio.id}`)
                .then(response => response.json())
                .then(pontuacoes => {
                    if (pontuacoes.success && pontuacoes.data) {
                        document.getElementById('editPontosDebito').value = pontuacoes.data.DEBITO_AUTOMATICO;
                        document.getElementById('editPontosCredito').value = pontuacoes.data.CREDITO_PESSOAL;
                        document.getElementById('editPontosCheque').value = pontuacoes.data.CHEQUE_ESPECIAL;
                        document.getElementById('editPontosCartao').value = pontuacoes.data.CARTAO_CREDITO;
                    }
                })
                .catch(error => {
                    console.error('Erro ao buscar pontuações:', error);
                    alert('Erro ao carregar as pontuações. Por favor, tente novamente.');
                });

            // Marcar campos como required
            const pontosInputs = pontuacaoSection.querySelectorAll('.pontos-input');
            pontosInputs.forEach(input => input.required = true);
        }

        // Mostrar o modal apenas depois de preparar todos os campos
        $('#editarDesafioModal').modal('show');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Toggle do campo de horário
        const autoFinalizarCheck = document.getElementById('autoFinalizar');
        const horarioFimGroup = document.getElementById('horarioFimGroup');
        
        if (autoFinalizarCheck && horarioFimGroup) {
            autoFinalizarCheck.addEventListener('change', function() {
                horarioFimGroup.style.display = this.checked ? 'block' : 'none';
            });
        }
        
        // Adicionar os mesmos campos no modal de edição
        const editAutoFinalizarCheck = document.getElementById('editAutoFinalizar');
        const editHorarioFimGroup = document.getElementById('editHorarioFimGroup');
        
        if (editAutoFinalizarCheck && editHorarioFimGroup) {
            editAutoFinalizarCheck.addEventListener('change', function() {
                editHorarioFimGroup.style.display = this.checked ? 'block' : 'none';
            });
        }
    });
    </script>

    <style>
    /* Estilos para os modais */
    .modal-content {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    .modal-header {
        background-color: var(--sicoob-dark-green);
        color: white;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        padding: 1rem 1.5rem;
    }
    
    .modal-title {
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .modal-header .close {
        color: white;
        opacity: 0.8;
        text-shadow: none;
        transition: opacity 0.2s;
    }
    
    .modal-header .close:hover {
        opacity: 1;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .modal-footer {
        border-top: 1px solid #eee;
        padding: 1rem 1.5rem;
    }
    
    .form-group label {
        color: var(--sicoob-dark);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 0.5rem 0.75rem;
    }
    
    .form-control:focus {
        border-color: var(--sicoob-turquoise);
        box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
    }
    
    .input-group-text {
        background-color: var(--sicoob-light);
        border: 1px solid #ddd;
        color: var(--sicoob-gray);
    }
    
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 600;
    }
    
    .btn-primary {
        background-color: var(--sicoob-turquoise);
        border-color: var(--sicoob-turquoise);
    }
    
    .btn-primary:hover {
        background-color: var(--sicoob-dark-green);
        border-color: var(--sicoob-dark-green);
    }
    
    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }
    
    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    /* Estilos para os badges de status */
    .badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        font-weight: 500;
        letter-spacing: 0.3px;
    }
    
    .badge i {
        font-size: 0.9em;
    }
    
    .badge-success {
        background-color: #28a745;
        color: white;
    }
    
    .badge-secondary {
        background-color: #6c757d;
        color: white;
    }
    
    .badge-info {
        background-color: #17a2b8;
        color: white;
    }

    /* Estilos para a coluna de ações e botões */
    .actions-column {
        white-space: nowrap;
        width: 1%;
        padding: 8px !important;
    }

    .action-buttons {
        display: flex;
        gap: 4px;
        align-items: center;
    }

    .action-buttons form {
        margin: 0;
    }

    .btn-icon {
        width: 32px;
        height: 32px;
        padding: 0 !important;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .btn-icon i {
        font-size: 14px;
    }

    /* Cores dos botões */
    .btn-primary {
        background-color: var(--sicoob-turquoise);
        border-color: var(--sicoob-turquoise);
    }

    .btn-primary:hover {
        background-color: var(--sicoob-dark-green);
        border-color: var(--sicoob-dark-green);
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .btn-warning {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #212529;
    }

    .btn-warning:hover {
        background-color: #e0a800;
        border-color: #d39e00;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    /* Ajustes da tabela */
    .table td {
        vertical-align: middle;
    }

    .table-responsive {
        margin-bottom: 1rem;
    }

    /* Tooltip nos botões */
    [title] {
        position: relative;
    }

    [title]:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 8px;
        background: rgba(0,0,0,0.8);
        color: white;
        font-size: 12px;
    }
    </style>
</body>
</html> 