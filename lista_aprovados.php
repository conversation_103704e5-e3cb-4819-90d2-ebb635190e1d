<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Obtém o PA do usuário logado
$pa = get_user_pa();
$is_admin = $_SESSION['is_admin'];

// Log para debug
error_log("Lista Aprovados: User={$_SESSION['user_id']}, IsAdmin={$is_admin}, PA={$pa}");

// Verifica se o PA está definido e ajusta a consulta
if (!$is_admin) {
    // Converte o PA do registro para 2 dígitos para comparação
    if ($pa) {
        $count_query .= " AND LPAD(a.pa, 2, '0') = ?";
    }
}