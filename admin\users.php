<?php
session_start();
require_once '../config/config.php';
require_once '../includes/auth.php';

// Log para debug
error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
error_log("POST Data: " . print_r($_POST, true));

// Verifica se é admin
check_admin();

// Garante que o banco de dados está configurado
setup_database();

// Debug para verificar o estado atual
$stmt = $pdo->prepare("SELECT * FROM campanha_config WHERE chave = 'desafio_ativo'");
$stmt->execute();
$debug_config = $stmt->fetch();
error_log("Debug - Configuração atual: " . print_r($debug_config, true));

// Processas as requisições POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = $_POST['username'];
        $pa = $_POST['pa'];
        $is_admin = isset($_POST['is_admin']) ? 1 : 0;
        $action = $_POST['action'];

        if ($action === 'add') {
            // Verifica se o usuário existe no LDAP
            $ldap = ldap_connect(LDAP_HOST, LDAP_PORT);
            ldap_set_option($ldap, LDAP_OPT_PROTOCOL_VERSION, LDAP_VERSION);
            ldap_set_option($ldap, LDAP_OPT_REFERRALS, 0);

            if (!$ldap) {
                throw new Exception("Erro ao conectar ao servidor LDAP");
            }

            // Tenta buscar o usuário no LDAP
            $filter = "(sAMAccountName=$username)";
            $result = @ldap_search($ldap, LDAP_BASE_DN, $filter);

            if (!$result || ldap_count_entries($ldap, $result) === 0) {
                throw new Exception("Usuário não encontrado no domínio");
            }

            // Adiciona o usuário no banco
            $stmt = $pdo->prepare("INSERT INTO campanha_users (username, pa, is_admin) VALUES (?, ?, ?)");
            $stmt->execute([$username, $pa, $is_admin]);
            $_SESSION['success_message'] = "Usuário adicionado com sucesso!";

        } elseif ($action === 'edit') {
            $id = $_POST['user_id'];
            $stmt = $pdo->prepare("UPDATE campanha_users SET pa = ?, is_admin = ? WHERE id = ?");
            $stmt->execute([$pa, $is_admin, $id]);
            $_SESSION['success_message'] = "Usuário atualizado com sucesso!";
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }

    header('Location: users.php');
    exit();
}

// Busca todos os usuários
$stmt = $pdo->prepare("SELECT * FROM campanha_users ORDER BY created_at DESC");
$stmt->execute();
$users = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Usuários - Desafio RPA</title>
    <link rel="icon" type="image/png" href="../assets/images/icon.png">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/table.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <img src="../assets/images/logo1.png" alt="Sicoob" class="logo">
            <?php include '../includes/menu.php'; ?>
        </div>
    </header>

    <main class="container">
        <div class="page-header">
            <h1>Gerenciar Usuários</h1>
            <button onclick="showAddModal()" class="btn btn-primary">
                <i class="fas fa-user-plus"></i>
                Adicionar Usuário
            </button>
        </div>

        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="success-message">
                <?php 
                echo $_SESSION['success_message'];
                unset($_SESSION['success_message']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="error-message">
                <?php 
                echo $_SESSION['error_message'];
                unset($_SESSION['error_message']);
                ?>
            </div>
        <?php endif; ?>

        <div class="content">
            <!-- Lista de Usuários -->
            <div class="users-list">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> Usuários Cadastrados</h2>
                </div>
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-toolbar">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="searchInput" placeholder="Buscar usuário..." class="form-control">
                            </div>
                            <span class="total-records">
                                <i class="fas fa-user-check"></i>
                                Total: <?php echo count($users); ?> usuários
                            </span>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Usuário</th>
                                    <th>PA</th>
                                    <th>Admin</th>
                                    <th>Data de Cadastro</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td class="text-center"><?php echo htmlspecialchars($user['pa']); ?></td>
                                        <td class="text-center">
                                            <?php if ($user['is_admin']): ?>
                                                <span class="status status-sim">Sim</span>
                                            <?php else: ?>
                                                <span class="status status-nao">Não</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                                        <td class="actions-cell">
                                            <button onclick="editUser(<?php echo htmlspecialchars(json_encode($user)); ?>)"
                                                    class="btn btn-secondary btn-sm">
                                                <i class="fas fa-edit"></i>
                                                Editar
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal de Adição -->
    <div id="add-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Adicionar Novo Usuário</h3>
                <p class="modal-subtitle">Preencha os dados do novo usuário</p>
            </div>
            <form method="POST" action="">
                <input type="hidden" name="action" value="add">
                
                <div class="form-group">
                    <label for="add-username">Usuário do Domínio:</label>
                    <input type="text" id="add-username" name="username" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="add-pa">PA:</label>
                    <input type="text" id="add-pa" name="pa" class="form-control" required>
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="is_admin" id="add-is-admin">
                        <span>Administrador</span>
                    </label>
                </div>

                <div class="form-actions">
                    <button type="button" onclick="closeModal('add-modal')" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Adicionar Usuário
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="edit-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Usuário</h3>
                <p class="modal-subtitle">Altere os dados do usuário</p>
            </div>
            <form method="POST" action="">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="user_id" id="edit-user-id">

                <div class="form-group">
                    <label>Usuário:</label>
                    <input type="text" id="edit-username" disabled class="form-control">
                </div>

                <div class="form-group">
                    <label for="edit-pa">PA:</label>
                    <input type="text" id="edit-pa" name="pa" class="form-control" required>
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="edit-is-admin" name="is_admin">
                        <span>Administrador</span>
                    </label>
                </div>

                <div class="form-actions">
                    <button type="button" onclick="closeModal('edit-modal')" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .page-header h1 {
        margin: 0;
        color: var(--sicoob-turquoise);
    }

    .section-header {
        margin-bottom: 1.5rem;
    }

    .section-header h2 {
        color: var(--sicoob-dark-green);
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-header h2 i {
        color: var(--sicoob-turquoise);
    }

    .form-section {
        background: white;
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        margin-bottom: 2rem;
        box-shadow: var(--shadow-sm);
    }

    .add-user-form {
        max-width: 100%;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .checkbox-group {
        display: flex;
        align-items: flex-end;
        margin-bottom: 0;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
    }

    .checkbox-label input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        margin: 0;
    }

    .users-list {
        margin-top: 2rem;
    }

    .table-container {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
    }

    .status {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-sm);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-sim {
        background: var(--sicoob-light-green);
        color: var(--sicoob-dark-green);
    }

    .status-nao {
        background: var(--sicoob-gray-200);
        color: var(--sicoob-gray-600);
    }

    .actions-cell {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .actions-cell .btn {
        padding: 0.25rem 0.5rem;
    }

    .text-center {
        text-align: center;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }

        .checkbox-group {
            margin-top: 1rem;
        }
    }

    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    .modal.show {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .modal-content {
        background: white;
        padding: 2rem;
        border-radius: var(--radius-lg);
        width: 100%;
        max-width: 500px;
        position: relative;
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        margin-bottom: 1.5rem;
    }

    .modal-header h3 {
        color: var(--sicoob-dark-green);
        margin: 0;
    }

    .modal-subtitle {
        color: var(--sicoob-gray-600);
        margin: 0.5rem 0 0;
        font-size: 0.875rem;
    }

    .admin-controls {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .status-form {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 1rem;
    }

    .status-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .status-label {
        font-weight: 600;
    }

    .status-value {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 600;
    }

    .status-value.active {
        background-color: var(--sicoob-light-green);
        color: white;
    }

    .status-value.inactive {
        background-color: #dc3545;
        color: white;
    }

    .btn-toggle {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        background-color: var(--sicoob-turquoise);
        color: white;
        cursor: pointer;
        font-weight: 600;
        transition: background-color 0.3s;
    }

    .btn-toggle:hover {
        background-color: var(--sicoob-dark-green);
    }
    </style>

    <script>
    function showAddModal() {
        document.getElementById('add-modal').classList.add('show');
    }

    function editUser(user) {
        document.getElementById('edit-user-id').value = user.id;
        document.getElementById('edit-username').value = user.username;
        document.getElementById('edit-pa').value = user.pa;
        document.getElementById('edit-is-admin').checked = user.is_admin == 1;
        document.getElementById('edit-modal').classList.add('show');
    }

    function closeModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }

    // Fecha o modal se clicar fora dele
    window.onclick = function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove('show');
        }
    }

    // Adiciona a funcionalidade de busca
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchText = this.value.toLowerCase();
        const table = document.querySelector('table');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length; j++) {
                const cell = cells[j];
                if (cell.textContent.toLowerCase().indexOf(searchText) > -1) {
                    found = true;
                    break;
                }
            }

            row.style.display = found ? '' : 'none';
        }
    });
    </script>
</body>
</html> 