<?php
session_start();
require_once 'config/config.php';
require_once 'vendor/autoload.php'; // Para o PHPMailer
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    // Conectar ao LDAP
    $ldap = ldap_connect(LDAP_HOST, LDAP_PORT);
    ldap_set_option($ldap, LDAP_OPT_PROTOCOL_VERSION, LDAP_VERSION);
    ldap_set_option($ldap, LDAP_OPT_REFERRALS, 0);

    if ($ldap) {
        // Tenta autenticar o usuário
        $bind = @ldap_bind($ldap, $username . '@' . LDAP_HOST, $password);
        
        if ($bind) {
            // Autenticação bem sucedida
            $pa = get_pa_from_username($username);
            
            // Busca informações do usuário no LDAP
            $search = ldap_search($ldap, LDAP_BASE_DN, "(sAMAccountName=$username)");
            $entries = ldap_get_entries($ldap, $search);
            
            if ($entries['count'] > 0) {
                $email = isset($entries[0]['mail'][0]) ? $entries[0]['mail'][0] : null;
                
                if (!$email) {
                    $error = 'E-mail não cadastrado no LDAP. Por favor, entre em contato com o administrador.';
                    ldap_close($ldap);
                    goto show_form;
                }
                
                // Verifica se o usuário já existe na tabela campanha_users
                $stmt = $pdo->prepare("SELECT id, pa, is_admin FROM campanha_users WHERE username = ?");
                $stmt->execute([$username]);
                $user = $stmt->fetch();
                
                if ($user) {
                    // Se o usuário já existe, atribui o PA existente
                    $_SESSION['user_id'] = $username;
                    $_SESSION['is_admin'] = ($username === 'admin.pedro' || (bool)$user['is_admin']); // Verifica tanto o username quanto o flag do banco
                } else {
                    // Se não existir, cadastra o usuário com PA baseado no nome de usuário
                    $is_admin = ($username === 'admin.pedro');
                    $stmt = $pdo->prepare("INSERT INTO campanha_users (username, pa, is_admin) VALUES (?, ?, ?)");
                    $stmt->execute([
                        $username,
                        $pa ?? '00', // Usa o PA extraído do username ou '00' se não encontrar
                        $is_admin ? 1 : 0 // Garante valor numérico para is_admin
                    ]);
                    $_SESSION['user_id'] = $username;
                    $_SESSION['is_admin'] = $is_admin;
                }

                // Adiciona log para debug
                error_log("Login: User={$username}, IsAdmin=" . ($_SESSION['is_admin'] ? 'true' : 'false'));
                
                // Gera um código aleatório de 6 dígitos
                $code = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
                
                try {
                    // Configuração do PHPMailer
                    $mail = new PHPMailer(true);
                    $mail->isSMTP();
                    $mail->Host = SMTP_HOST;
                    $mail->SMTPAuth = true;
                    $mail->Username = SMTP_USER;
                    $mail->Password = SMTP_PASS;
                    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                    $mail->Port = SMTP_PORT;
                    $mail->CharSet = 'UTF-8';

                    // Configuração do e-mail
                    $mail->setFrom(SMTP_FROM_EMAIL, 'Desafio RPA - Sicoob');
                    $mail->addAddress($email);
                    $mail->isHTML(true);
                    $mail->Subject = 'Código de Verificação - Desafio RPA';
                    
                    // Carrega a imagem da logo e converte para base64
                    $logo_path = __DIR__ . '/assets/images/logo1.png';
                    $logo_data = '';
                    if (file_exists($logo_path)) {
                        $logo_data = base64_encode(file_get_contents($logo_path));
                    }
                    
                    // Template do e-mail com a imagem em base64
                    $mail->Body = "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>\n    <div style='background-color: #003641; padding: 20px; text-align: center;'>\n        <img src='data:image/png;base64," . $logo_data . "' alt='Sicoob' style='height: 50px;'>\n    </div>\n    <div style='padding: 20px; background-color: #f5f5f5;'>\n        <h2 style='color: #003641; margin-bottom: 20px;'>Código de Verificação</h2>\n        <p style='color: #666; margin-bottom: 20px;'>Seu código de verificação para acesso ao Desafio RPA é:</p>\n        <div style='background-color: #00AE9D; color: white; padding: 15px; font-size: 24px; text-align: center; border-radius: 5px; margin-bottom: 20px;'>\n            $code\n        </div>\n        <p style='color: #666; font-size: 12px;'>Este código é válido por 5 minutos. Não compartilhe este código com ninguém.</p>\n    </div>\n</div>";

                    $mail->send();
                    
                    // Armazena informações na sessão
                    $_SESSION['2fa_code'] = $code;
                    $_SESSION['2fa_time'] = time();
                    $_SESSION['temp_username'] = $username;
                    
                    // Redireciona para a página de verificação
                    header('Location: verify.php');
                    exit();
                    
                } catch (Exception $e) {
                    $error = 'Erro ao enviar o código de verificação: ' . $mail->ErrorInfo;
                }
            }
            
            ldap_close($ldap);
        } else {
            $error = 'Usuário ou senha inválidos';
        }
    } else {
        $error = 'Erro ao conectar ao servidor LDAP';
    }
}

show_form:
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Desafio Sicoob</title>
    <link rel="icon" type="image/png" href="assets/images/icon.png">
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="login-page">
    <div class="page-container">
        <div class="campaign-header">
            <h1>
                <span class="campaign-title">
                    <i class="fas fa-robot"></i>
                    ATUALIZAÇÃO CADASTRAL - RPA
                    <i class="fas fa-cogs"></i>
                </span>
                <span class="campaign-subtitle">
                    <i class="fas fa-sync"></i>
                    consulta aos cadastros atualizados via RPA
                    <i class="fas fa-chart-line"></i>
                </span>
            </h1>
        </div>

        <div class="login-container">
            <div class="login-box">
                <img src="assets/images/logo.png" alt="Sicoob" class="logo">
                <h2>Login do Desafio</h2>
                
                <?php if ($error): ?>
                    <div class="error-message"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="form-group">
                        <label for="username">Usuário:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Senha:</label>
                        <input type="password" id="password" name="password" required>
                    </div>

                    <button type="submit" class="btn-primary">Entrar</button>
                </form>
            </div>
        </div>
    </div>

    <style>
    .login-page {
        min-height: 100vh;
        background: var(--sicoob-light);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .page-container {
        width: 100%;
        max-width: 800px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .campaign-header {
        text-align: center;
        padding: 2.5rem;
        background: linear-gradient(135deg, var(--sicoob-turquoise) 0%, var(--sicoob-dark-green) 100%);
        border-radius: 8px;
        box-shadow: 0 8px 16px rgba(0, 54, 65, 0.15);
        position: relative;
        overflow: hidden;
        width: 100%;
    }

    .campaign-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--sicoob-light-green);
    }

    .campaign-header h1 {
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        position: relative;
    }

    .campaign-title {
        color: var(--sicoob-light-green);
        font-size: 2rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 2px;
        line-height: 1.2;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .campaign-title i {
        font-size: 2.5rem;
        animation: pulse 2s infinite;
    }

    .campaign-subtitle {
        color: white;
        font-size: 1.5rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .campaign-subtitle i {
        font-size: 1.25rem;
        opacity: 0.9;
    }

    .campaign-subtitle i.fa-sync {
        animation: spin 4s linear infinite;
    }

    .campaign-subtitle i.fa-chart-line {
        animation: slideUp 2s infinite;
    }

    .login-container {
        width: 100%;
        max-width: 400px;
    }

    .login-box {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        text-align: center;
    }

    .login-box .logo {
        height: 60px;
        margin-bottom: 1.5rem;
    }

    .login-box h2 {
        color: var(--sicoob-dark-green);
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
        text-align: left;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: var(--sicoob-gray);
        font-weight: 600;
    }

    .form-group input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        transition: border-color 0.2s;
    }

    .form-group input:focus {
        border-color: var(--sicoob-turquoise);
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 174, 157, 0.1);
    }

    .btn-primary {
        background: var(--sicoob-turquoise);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 4px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s;
        width: 100%;
    }

    .btn-primary:hover {
        background: var(--sicoob-dark-green);
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    @keyframes slideUp {
        0% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
        100% { transform: translateY(0); }
    }

    @media (max-width: 768px) {
        .login-page {
            padding: 1rem;
        }

        .page-container {
            gap: 1.5rem;
        }

        .campaign-header {
            padding: 2rem 1.5rem;
        }

        .campaign-title {
            font-size: 2rem;
            letter-spacing: 1px;
        }
        
        .campaign-title i {
            font-size: 1.75rem;
        }

        .campaign-subtitle {
            font-size: 1.125rem;
        }

        .campaign-subtitle i {
            font-size: 1rem;
        }

        .login-box {
            padding: 1.5rem;
        }
    }
    </style>
</body>
</html>