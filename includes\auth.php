<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/db.php';

function check_auth() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }

    // Se não for admin, verifica o status do desafio
    if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        check_desafio_status();
    }
}

function check_admin() {
    check_auth();
    if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        // Adiciona log para debug
        error_log("Admin Check Failed: User={$_SESSION['user_id']}, IsAdmin=" . (isset($_SESSION['is_admin']) ? $_SESSION['is_admin'] : 'not_set'));
        header('Location: index.php');
        exit();
    }
}

function check_desafio_status() {
    // Se for admin, não verifica o status
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true) {
        return;
    }
    
    // Lista de páginas permitidas mesmo sem desafio ativo
    $allowed_pages = ['desafio_inativo.php', 'logout.php', 'index.php'];
    $current_script = basename($_SERVER['SCRIPT_NAME']);
    
    // Adiciona log para debug
    error_log("Verificando acesso à página: " . $current_script);
    
    // Se for uma página permitida, permite o acesso
    if (in_array($current_script, $allowed_pages)) {
        error_log("Página permitida sem desafio ativo");
        return;
    }
    
    // Verifica se o sistema está ativo
    $desafio = get_desafio_ativo();
    $sistema_ativo = is_sistema_ativo();
    
    error_log("Status do sistema - Desafio: " . ($desafio ? "Encontrado" : "Não encontrado") . ", Sistema ativo: " . ($sistema_ativo ? "Sim" : "Não"));
    
    // Se não há desafio ativo, redireciona para página de desafio inativo
    if (!$desafio) {
        error_log("Acesso negado - Nenhum desafio ativo");
        header('Location: desafio_inativo.php');
        exit();
    }
    
    // Lista de páginas que requerem sistema totalmente ativo
    $restricted_pages = ['registrar_venda.php', 'minhas_vendas.php'];
    $requires_active_system = in_array($current_script, $restricted_pages);
    
    error_log("Página atual: " . $current_script . ", Requer sistema ativo: " . ($requires_active_system ? "Sim" : "Não"));
    
    // Se a página requer sistema ativo e o sistema não está ativo, redireciona
    if ($requires_active_system && !$sistema_ativo) {
        error_log("Acesso negado - Sistema não está ativo para páginas restritas");
        header('Location: index.php');
        exit();
    }
}

function get_user_pa() {
    global $pdo;
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    // Se for admin, retorna null para ter acesso a todos os PAs
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true) {
        error_log("PA Check: User={$_SESSION['user_id']}, IsAdmin=true, returning null PA");
        return null;
    }
    
    // Para usuários normais, busca o PA do banco
    $stmt = $pdo->prepare("SELECT pa, is_admin FROM campanha_users WHERE username = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $result = $stmt->fetch();
    
    // Se o usuário for admin no banco, também retorna null
    if ($result && ($result['is_admin'] == 1 || $_SESSION['user_id'] === 'admin.pedro')) {
        error_log("PA Check: User={$_SESSION['user_id']}, IsAdmin from DB=true, returning null PA");
        $_SESSION['is_admin'] = true; // Atualiza a sessão caso necessário
        return null;
    }
    
    error_log("PA Check: User={$_SESSION['user_id']}, PA=" . ($result ? $result['pa'] : 'null'));
    return $result ? $result['pa'] : null;
}

// Função para criar as tabelas necessárias se não existirem
function setup_database() {
    global $pdo;
    
    // Tabela de usuários da campanha
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        pa VARCHAR(10) NOT NULL,
        is_admin BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Tabela de configurações do sistema
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        chave VARCHAR(50) NOT NULL UNIQUE,
        valor TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // Inserir configuração padrão do status do desafio
    $stmt = $pdo->prepare("INSERT IGNORE INTO campanha_config (chave, valor) VALUES (?, ?)");
    $stmt->execute(['desafio_ativo', 'true']);

    // Tabela de vendas da campanha
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_vendas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        cpf VARCHAR(11) NOT NULL,
        produto VARCHAR(100) NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        pa VARCHAR(10) NOT NULL,
        usuario VARCHAR(100) NOT NULL,
        data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP NULL,
        FOREIGN KEY (usuario) REFERENCES campanha_users(username)
    )");

    // Tabela de contatos realizados
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_contatos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        cpf VARCHAR(11) NOT NULL,
        usuario VARCHAR(100) NOT NULL,
        data_contato TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario) REFERENCES campanha_users(username)
    )");

    // Inserir usuário admin padrão se não existir
    $stmt = $pdo->prepare("INSERT IGNORE INTO campanha_users (username, pa, is_admin) VALUES (?, ?, ?)");
    $stmt->execute(['admin', '00', true]);
}

// Criar as tabelas ao carregar o arquivo
setup_database(); 