<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/db.php';

function check_auth() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }
}

function check_admin() {
    check_auth();
    if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        header('Location: index.php');
        exit();
    }
}



function get_user_pa() {
    global $pdo;
    if (!isset($_SESSION['user_id'])) {
        return null;
    }

    // Se for admin, retorna null para ter acesso a todos os PAs
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true) {
        return null;
    }

    // Para usuários normais, busca o PA do banco
    $stmt = $pdo->prepare("SELECT pa, is_admin FROM campanha_users WHERE username = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $result = $stmt->fetch();

    // Se o usuário for admin no banco, também retorna null
    if ($result && ($result['is_admin'] == 1 || $_SESSION['user_id'] === 'admin.pedro')) {
        $_SESSION['is_admin'] = true; // Atualiza a sessão caso necessário
        return null;
    }

    return $result ? $result['pa'] : null;
}

// Função para criar as tabelas necessárias se não existirem
function setup_database() {
    global $pdo;
    
    // Tabela de usuários da campanha
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        pa VARCHAR(10) NOT NULL,
        is_admin BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Tabela de configurações do sistema
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        chave VARCHAR(50) NOT NULL UNIQUE,
        valor TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");



    // Inserir usuário admin padrão se não existir
    $stmt = $pdo->prepare("INSERT IGNORE INTO campanha_users (username, pa, is_admin) VALUES (?, ?, ?)");
    $stmt->execute(['admin', '00', true]);
}

// Criar as tabelas ao carregar o arquivo
setup_database(); 