<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado e é administrador
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    $_SESSION['error_message'] = "Acesso negado. Você precisa estar logado como administrador.";
    header('Location: login.php');
    exit();
}

// Função para exibir mensagens
function show_message($message, $type = 'success') {
    $bg_color = $type === 'success' ? '#dff0d8' : '#f2dede';
    $text_color = $type === 'success' ? '#3c763d' : '#a94442';
    echo "<div style='background-color: {$bg_color}; color: {$text_color}; padding: 15px; margin: 20px; border-radius: 4px;'>";
    echo $message;
    echo "</div>";
}

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. Adicionar coluna descricao na tabela desafios
    $pdo->exec("ALTER TABLE desafios ADD COLUMN IF NOT EXISTS descricao TEXT AFTER nome");
    show_message("Coluna descricao adicionada à tabela desafios");

    // 2. Adicionar coluna tipo_ranking na tabela desafios
    $pdo->exec("ALTER TABLE desafios ADD COLUMN IF NOT EXISTS tipo_ranking ENUM('vendas', 'pontos') DEFAULT 'vendas' AFTER meta_vendas");
    show_message("Coluna tipo_ranking adicionada à tabela desafios");

    // 3. Modificar coluna meta_vendas para permitir NULL
    $pdo->exec("ALTER TABLE desafios MODIFY COLUMN meta_vendas DECIMAL(10,2) NULL");
    show_message("Coluna meta_vendas modificada para aceitar NULL");

    // 4. Criar tabela de pontuações
    $sql = "CREATE TABLE IF NOT EXISTS desafio_pontuacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        desafio_id INT NOT NULL,
        produto VARCHAR(50) NOT NULL,
        pontos INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (desafio_id) REFERENCES desafios(id),
        UNIQUE KEY unique_desafio_produto (desafio_id, produto)
    )";
    $pdo->exec($sql);
    show_message("Tabela desafio_pontuacoes criada com sucesso");

    // 5. Inserir pontuações padrão para teste
    $desafio_teste = $pdo->query("SELECT id FROM desafios WHERE status = 'ativo' LIMIT 1")->fetch();
    
    if ($desafio_teste) {
        $pontuacoes = [
            'DEBITO_AUTOMATICO' => 1,
            'CREDITO_PESSOAL' => 10,
            'CHEQUE_ESPECIAL' => 5,
            'CARTAO_CREDITO' => 5
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO desafio_pontuacoes (desafio_id, produto, pontos) VALUES (?, ?, ?)");
        
        foreach ($pontuacoes as $produto => $pontos) {
            $stmt->execute([$desafio_teste['id'], $produto, $pontos]);
        }
        show_message("Pontuações padrão inseridas para o desafio ativo");
    }

    show_message("Todas as alterações foram concluídas com sucesso!");

} catch (Exception $e) {
    show_message("Erro durante a atualização: " . $e->getMessage(), 'error');
}
?> 