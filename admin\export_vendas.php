<?php
session_start();
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Verifica se é admin
check_admin();

// Obtém o desafio selecionado
$desafio_id = isset($_GET['desafio']) ? (int)$_GET['desafio'] : null;

if (!$desafio_id) {
    $_SESSION['error_message'] = "Desafio não especificado para exportação.";
    header('Location: vendas.php');
    exit();
}

// Busca informações do desafio atual
$stmt = $pdo->prepare("SELECT nome, tipo_ranking FROM desafios WHERE id = ?");
$stmt->execute([$desafio_id]);
$desafio = $stmt->fetch();

if (!$desafio) {
    $_SESSION['error_message'] = "Desafio não encontrado.";
    header('Location: vendas.php');
    exit();
}

// Query base para buscar as vendas
$query = "SELECT 
    v.data_venda,
    v.cpf,
    a.nome,
    v.pa,
    v.usuario,
    v.produto,
    v.valor";

// Adiciona coluna de pontos se o desafio for por pontuação
if ($desafio['tipo_ranking'] === 'pontos') {
    $query .= ", COALESCE((
        SELECT dp.pontos 
        FROM desafio_pontuacoes dp 
        WHERE dp.desafio_id = v.desafio_id 
        AND dp.produto = v.produto
    ), 0) as pontos";
}

$query .= " FROM campanha_vendas v
    JOIN associados a ON v.cpf = a.cpf
    WHERE v.desafio_id = ?
    ORDER BY v.data_venda DESC";

$stmt = $pdo->prepare($query);
$stmt->execute([$desafio_id]);
$vendas = $stmt->fetchAll();

// Lista de produtos para exibição
$produtos = [
    'CREDITO_PESSOAL' => 'Crédito Pessoal',
    'CHEQUE_ESPECIAL' => 'Cheque Especial',
    'CARTAO_CREDITO' => 'Cartão de Crédito',
    'DEBITO_AUTOMATICO' => 'Débito Automático'
];

// Configurar cabeçalho para download do Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="vendas_' . strtolower(str_replace(' ', '_', $desafio['nome'])) . '.xls"');
header('Cache-Control: max-age=0');

// Criar cabeçalho do Excel
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "</head>";
echo "<body>";
echo "<table border='1'>";

// Cabeçalhos das colunas
echo "<tr>";
echo "<th>Data</th>";
echo "<th>CPF</th>";
echo "<th>Nome</th>";
echo "<th>PA</th>";
echo "<th>Usuário</th>";
echo "<th>Produto</th>";
echo "<th>Valor</th>";
if ($desafio['tipo_ranking'] === 'pontos') {
    echo "<th>Pontos</th>";
}
echo "</tr>";

// Dados
foreach ($vendas as $venda) {
    echo "<tr>";
    echo "<td>" . date('d/m/Y H:i', strtotime($venda['data_venda'])) . "</td>";
    echo "<td>" . mask_cpf($venda['cpf']) . "</td>";
    echo "<td>" . htmlspecialchars($venda['nome']) . "</td>";
    echo "<td>" . htmlspecialchars($venda['pa']) . "</td>";
    echo "<td>" . htmlspecialchars($venda['usuario']) . "</td>";
    echo "<td>" . $produtos[$venda['produto']] . "</td>";
    echo "<td>";
    if ($venda['produto'] === 'DEBITO_AUTOMATICO') {
        echo "Ativo";
    } else {
        echo "R$ " . number_format($venda['valor'], 2, ',', '.');
    }
    echo "</td>";
    if ($desafio['tipo_ranking'] === 'pontos') {
        echo "<td>" . $venda['pontos'] . "</td>";
    }
    echo "</tr>";
}

echo "</table>";
echo "</body>";
echo "</html>";
exit();
?>
