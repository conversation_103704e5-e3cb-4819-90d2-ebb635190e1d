<?php
$current_file = basename($_SERVER['PHP_SELF']);
$is_admin_area = strpos($_SERVER['PHP_SELF'], '/admin/') !== false;
$base_path = $is_admin_area ? '../' : '';
?>
<nav>
    <ul>
        <li><a href="<?php echo $base_path; ?>index.php" <?php echo $current_file === 'index.php' ? 'class="active"' : ''; ?>>Listagem</a></li>
        <?php if ($_SESSION['is_admin']): ?>
        <li><a href="<?php echo $base_path; ?>admin/users.php" <?php echo $current_file === 'users.php' ? 'class="active"' : ''; ?>>Usuários</a></li>
        <?php endif; ?>
        <li><a href="<?php echo $base_path; ?>logout.php">Sair</a></li>
    </ul>
</nav>
<div class="user-info" style="color: white; font-weight: bold;">
    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_id']); ?>!</span>
</div>