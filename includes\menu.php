<?php
$current_file = basename($_SERVER['PHP_SELF']);
$is_admin_area = strpos($_SERVER['PHP_SELF'], '/admin/') !== false;
$base_path = $is_admin_area ? '../' : '';
?>
<nav>
    <ul>
        <li><a href="<?php echo $base_path; ?>index.php" <?php echo $current_file === 'index.php' ? 'class="active"' : ''; ?>>Listagem</a></li>
        <li><a href="<?php echo $base_path; ?>minhas_vendas.php" <?php echo $current_file === 'minhas_vendas.php' ? 'class="active"' : ''; ?>>Minhas Vendas</a></li>
        <?php if ($_SESSION['is_admin']): ?>
        <li><a href="<?php echo $base_path; ?>ranking_publico.php" target="_blank" <?php echo $current_file === 'ranking.php' ? 'class="active"' : ''; ?>>Ranking</a></li>
        <li><a href="<?php echo $base_path; ?>admin/users.php" <?php echo $current_file === 'users.php' ? 'class="active"' : ''; ?>>Usuários</a></li>
        <li><a href="<?php echo $base_path; ?>admin/vendas.php" <?php echo $current_file === 'vendas.php' ? 'class="active"' : ''; ?>>Vendas</a></li>
        <li><a href="<?php echo $base_path; ?>admin/desafios.php" <?php echo $current_file === 'desafios.php' ? 'class="active"' : ''; ?>>Gestão de Desafios</a></li>
        <?php endif; ?>
        <li><a href="<?php echo $base_path; ?>logout.php">Sair</a></li>
    </ul>
</nav>
<div class="user-info" style="color: white; font-weight: bold;">
    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_id']); ?>!</span>
</div> 