<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado
check_auth();

// Verifica se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit();
}

// Verifica se o sistema está ativo
if (!is_sistema_ativo()) {
    header('Location: desafio_inativo.php');
    exit();
}

// Obtém os dados do formulário
$cpf = $_POST['cpf'];
$produto = $_POST['produto'];
$valor = $produto === 'DEBITO_AUTOMATICO' ? 1 : floatval($_POST['valor']);
$usuario = $_SESSION['user_id'];
$pa = get_user_pa(); // Busca o PA atual do banco
$is_admin = $_SESSION['is_admin'];
$tem_debito = $_POST['tem_debito'] === 'Sim';

try {
    // Inicia a transação
    $pdo->beginTransaction();

    // Verifica se existe um desafio ativo, considerando o horário de fim
    $stmt = $pdo->prepare("
        SELECT id FROM desafios 
        WHERE status = 'ativo' 
        AND data_inicio <= CURDATE()
        AND (
            data_fim > CURDATE()
            OR (
                data_fim = CURDATE()
                AND (
                    horario_fim IS NULL 
                    OR TIME(horario_fim) > CURRENT_TIME()
                )
            )
        )
    ");
    $stmt->execute();
    $desafio = $stmt->fetch();
    
    if (!$desafio) {
        throw new Exception("Não há desafio ativo no momento.");
    }
    
    $desafio_id = $desafio['id'];

    // Verifica se o CPF existe na tabela de associados e está aprovado
    $stmt = $pdo->prepare("SELECT pa, debito_automatico FROM associados WHERE cpf = ? AND status IN (3, 8)");
    $stmt->execute([$cpf]);
    $associado = $stmt->fetch();
    
    if (!$associado) {
        throw new Exception("CPF não encontrado ou não está aprovado.");
    }

    // Verifica se o usuário tem permissão para registrar venda para este PA
    // Administradores podem registrar vendas para qualquer PA
    if (!$is_admin) {
        if ($pa === null || ($pa !== '00' && ltrim($associado['pa'], '0') !== ltrim($pa, '0'))) {
            throw new Exception("Você não tem permissão para registrar venda para este PA.");
        }
    }

    // Se for Débito Automático, verifica se já está ativo
    if ($produto === 'DEBITO_AUTOMATICO' && $tem_debito) {
        throw new Exception("Débito Automático já está ativo para este associado.");
    }

    // Verifica se já existe uma venda deste produto para este CPF no desafio atual
    $stmt = $pdo->prepare("SELECT id, valor FROM campanha_vendas WHERE cpf = ? AND produto = ? AND desafio_id = ?");
    $stmt->execute([$cpf, $produto, $desafio_id]);
    $venda_existente = $stmt->fetch();

    if ($venda_existente) {
        // Atualiza o valor da venda existente
        $stmt = $pdo->prepare("UPDATE campanha_vendas SET valor = ?, data_atualizacao = NOW() WHERE id = ?");
        $stmt->execute([$valor, $venda_existente['id']]);
        $mensagem = "Valor da venda atualizado com sucesso!";
    } else {
        // Registra uma nova venda
        $stmt = $pdo->prepare("INSERT INTO campanha_vendas (cpf, produto, valor, pa, usuario, desafio_id) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$cpf, $produto, $valor, $pa, $usuario, $desafio_id]);
        $mensagem = "Venda registrada com sucesso!";
    }

    // Confirma a transação
    $pdo->commit();

    // Redireciona com mensagem de sucesso
    $_SESSION['success_message'] = $mensagem;
    header('Location: index.php');
    exit();

} catch (Exception $e) {
    // Desfaz a transação em caso de erro
    $pdo->rollBack();
    
    // Redireciona com mensagem de erro
    $_SESSION['error_message'] = $e->getMessage();
    header('Location: index.php');
    exit();
} 