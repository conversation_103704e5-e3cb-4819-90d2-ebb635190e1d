<?php
session_start();
require_once '../config/config.php';
require_once '../includes/auth.php';

// Verifica se é admin
check_admin();

// Verifica se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error_message'] = "Método inválido";
    header('Location: desafios.php');
    exit();
}

try {
    $pdo->beginTransaction();

    $acao = $_POST['acao'];
    $nome = $_POST['nome'];
    $descricao = $_POST['descricao'];
    $data_inicio = $_POST['data_inicio'];
    $data_fim = $_POST['data_fim'];
    $meta_vendas = (int)$_POST['meta_vendas'];
    $tipo_ranking = $_POST['tipo_ranking'];

    if ($acao === 'criar') {
        // Insere o novo desafio
        $stmt = $pdo->prepare("
            INSERT INTO desafios (nome, descricao, data_inicio, data_fim, meta_vendas, tipo_ranking)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$nome, $descricao, $data_inicio, $data_fim, $meta_vendas, $tipo_ranking]);
        
        $desafio_id = $pdo->lastInsertId();
    } else if ($acao === 'atualizar') {
        $desafio_id = (int)$_POST['desafio_id'];
        
        // Atualiza o desafio
        $stmt = $pdo->prepare("
            UPDATE desafios 
            SET nome = ?, descricao = ?, data_inicio = ?, data_fim = ?, 
                meta_vendas = ?, tipo_ranking = ?
            WHERE id = ?
        ");
        $stmt->execute([$nome, $descricao, $data_inicio, $data_fim, $meta_vendas, $tipo_ranking, $desafio_id]);
        
        // Remove pontuações antigas se existirem
        $stmt = $pdo->prepare("DELETE FROM desafio_pontuacoes WHERE desafio_id = ?");
        $stmt->execute([$desafio_id]);
    }

    // Se for ranking por pontos, insere as pontuações
    if ($tipo_ranking === 'pontos' && isset($_POST['pontos'])) {
        $stmt = $pdo->prepare("
            INSERT INTO desafio_pontuacoes (desafio_id, produto, pontos)
            VALUES (?, ?, ?)
        ");
        
        foreach ($_POST['pontos'] as $produto => $pontos) {
            $stmt->execute([$desafio_id, $produto, (int)$pontos]);
        }
    }

    $pdo->commit();
    $_SESSION['success_message'] = ($acao === 'criar' ? "Desafio criado" : "Desafio atualizado") . " com sucesso!";

} catch (Exception $e) {
    $pdo->rollBack();
    $_SESSION['error_message'] = "Erro ao " . ($acao === 'criar' ? "criar" : "atualizar") . " desafio: " . $e->getMessage();
}

header('Location: desafios.php');
exit(); 