<?php
require_once '../config/config.php';

try {
    // Inicia a transação
    $pdo->beginTransaction();

    // Verifica se a coluna auto_finalizar já existe
    $stmt = $pdo->query("SHOW COLUMNS FROM desafios LIKE 'auto_finalizar'");
    if ($stmt->rowCount() == 0) {
        // Adiciona a coluna auto_finalizar
        $pdo->exec("ALTER TABLE desafios ADD COLUMN auto_finalizar TINYINT(1) DEFAULT 0");
        echo "Coluna 'auto_finalizar' adicionada com sucesso!\n";
    } else {
        echo "Coluna 'auto_finalizar' já existe.\n";
    }

    // Verifica se a coluna horario_fim já existe
    $stmt = $pdo->query("SHOW COLUMNS FROM desafios LIKE 'horario_fim'");
    if ($stmt->rowCount() == 0) {
        // Adiciona a coluna horario_fim
        $pdo->exec("ALTER TABLE desafios ADD COLUMN horario_fim TIME DEFAULT '23:59:59'");
        echo "Coluna 'horario_fim' adicionada com sucesso!\n";
    } else {
        echo "Coluna 'horario_fim' já existe.\n";
    }

    // Commit das alterações
    $pdo->commit();
    echo "Atualização da tabela concluída com sucesso!";

} catch (Exception $e) {
    // Em caso de erro, faz rollback
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "Erro ao atualizar a tabela: " . $e->getMessage();
} 