<?php
session_start();
require_once 'config/config.php';
require_once 'vendor/autoload.php';
require_once 'includes/functions.php';
require_once 'includes/db.php';

use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

// Verifica se existe um código de verificação na sessão
if (!isset($_SESSION['2fa_code']) || !isset($_SESSION['temp_username'])) {
    header('Location: login.php');
    exit();
}

$error = '';
$resent = false;

// Verifica se o código expirou (5 minutos)
if (isset($_SESSION['2fa_time']) && (time() - $_SESSION['2fa_time']) > 300) {
    $error = 'O código expirou. Por favor, solicite um novo código.';
    unset($_SESSION['2fa_code']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['resend'])) {
        // Gera um novo código
        $code = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
        
        try {
            // Configuração do PHPMailer
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->Host = SMTP_HOST;
            $mail->SMTPAuth = true;
            $mail->Username = SMTP_USER;
            $mail->Password = SMTP_PASS;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = SMTP_PORT;
            $mail->CharSet = 'UTF-8';

            // Verifica a conexão LDAP
            $ldap = ldap_connect(LDAP_HOST, LDAP_PORT);
            if (!$ldap) {
                $error = 'Erro ao conectar ao servidor LDAP.';
            } else {
                ldap_set_option($ldap, LDAP_OPT_PROTOCOL_VERSION, LDAP_VERSION);
                ldap_set_option($ldap, LDAP_OPT_REFERRALS, 0);
                // Busca o e-mail do usuário no LDAP
                $search = ldap_search($ldap, LDAP_BASE_DN, "(sAMAccountName={$_SESSION['temp_username']})");
                if ($search === false) {
                    $error = 'Erro na busca LDAP: ' . ldap_error($ldap);
                } else {
                    $entries = ldap_get_entries($ldap, $search);
                    if ($entries === false) {
                        $error = 'Erro ao obter entradas LDAP: ' . ldap_error($ldap);
                    } else {
                        if (isset($entries[0]['mail'][0])) {
                            $email = $entries[0]['mail'][0];
                        } else {
                            $error = 'E-mail não encontrado para o usuário.';
                        }
                    }
                }
            }

            // Configuração do e-mail
            $mail->setFrom(SMTP_FROM_EMAIL, 'Desafio RPA - Sicoob');
            $mail->addAddress($email);
            $mail->isHTML(true);
            $mail->Subject = 'Novo Código de Verificação - Desafio RPA';
            
            // Carrega a imagem da logo e converte para base64
            $logo_path = __DIR__ . '/assets/images/logo1.png';
            $logo_data = '';
            if (file_exists($logo_path)) {
                $logo_data = base64_encode(file_get_contents($logo_path));
            }
            
            // Template do e-mail com a imagem em base64
            $mail->Body = "
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <div style='background-color: #003641; padding: 20px; text-align: center;'>
                        <img src='data:image/png;base64," . $logo_data . "' alt='Sicoob' style='height: 50px;'>
                    </div>
                    <div style='padding: 20px; background-color: #f5f5f5;'>
                        <h2 style='color: #003641; margin-bottom: 20px;'>Novo Código de Verificação</h2>
                        <p style='color: #666; margin-bottom: 20px;'>Seu novo código de verificação para acesso ao Desafio RPA é:</p>
                        <div style='background-color: #00AE9D; color: white; padding: 15px; font-size: 24px; text-align: center; border-radius: 5px; margin-bottom: 20px;'>
                            $code
                        </div>
                        <p style='color: #666; font-size: 12px;'>Este código é válido por 5 minutos. Não compartilhe este código com ninguém.</p>
                    </div>
                </div>
            ";

            $mail->send();
            
            // Atualiza o código na sessão
            $_SESSION['2fa_code'] = $code;
            $_SESSION['2fa_time'] = time();
            $resent = true;
            
        } catch (Exception $e) {
            $error = 'Erro ao reenviar o código. Por favor, tente novamente.';
        }
    } else {
        $input_code = $_POST['code'];
        
        if ($input_code === $_SESSION['2fa_code']) {
            // Autenticação bem sucedida
            $_SESSION['user_id'] = $_SESSION['temp_username'];
            unset($_SESSION['2fa_code']);
            unset($_SESSION['2fa_time']);
            
            // Verifica se o usuário é admin
            $stmt = $pdo->prepare("SELECT is_admin FROM campanha_users WHERE username = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();
            
            // Define is_admin baseado no username ou no banco
            $_SESSION['is_admin'] = ($_SESSION['user_id'] === 'admin.pedro' || (bool)$user['is_admin']);
            
            // Adiciona log para debug
            error_log("2FA Verify: User={$_SESSION['user_id']}, IsAdmin=" . ($_SESSION['is_admin'] ? 'true' : 'false'));
            

            
            unset($_SESSION['temp_username']);
            header('Location: index.php');
            exit();
        } else {
            $error = 'Código incorreto. Tente novamente.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação - Desafio Sicoob</title>
    <link rel="icon" type="image/png" href="assets/images/icon.png">
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-box">
            <img src="assets/images/logo.png" alt="Sicoob" class="logo">
            <h2>Verificação em Duas Etapas</h2>
            
            <?php if ($error): ?>
                <div class="error-message"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <?php if ($resent): ?>
                <div class="success-message">Novo código enviado com sucesso!</div>
            <?php endif; ?>

            <p class="verification-info">
                Digite o código de 6 dígitos enviado para seu e-mail.
                <?php if (isset($_SESSION['2fa_time'])): ?>
                    <br>
                    <small>
                        Tempo restante: <span id="timer">5:00</span>
                    </small>
                <?php endif; ?>
            </p>

            <form method="POST" action="" class="verification-form">
                <div class="code-input-container">
                    <input type="text" name="code" pattern="[0-9]{6}" maxlength="6" required
                           class="code-input" placeholder="000000" autocomplete="off">
                </div>

                <button type="submit" class="btn-primary">
                    <i class="fas fa-check-circle"></i>
                    Verificar
                </button>
            </form>

            <div class="back-to-login">
                <a href="login.php" class="btn-link">
                    <i class="fas fa-arrow-left"></i>
                    Voltar para o Login
                </a>
            </div>
        </div>
    </div>

    <style>
    .verification-info {
        color: var(--sicoob-gray-600);
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .code-input-container {
        margin-bottom: 1.5rem;
    }

    .code-input {
        font-family: monospace;
        font-size: 2rem;
        letter-spacing: 0.5rem;
        text-align: center;
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--sicoob-gray-300);
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .code-input:focus {
        border-color: var(--sicoob-turquoise);
        box-shadow: 0 0 0 3px rgba(0, 174, 157, 0.1);
        outline: none;
    }

    .success-message {
        background-color: var(--sicoob-light-green);
        color: var(--sicoob-dark-green);
        padding: 1rem;
        border-radius: 4px;
        margin-bottom: 1.5rem;
        text-align: center;
        font-weight: 600;
    }

    .btn-secondary {
        background: var(--sicoob-gray-200);
        color: var(--sicoob-gray-700);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 4px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        width: 100%;
        margin-top: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-secondary:hover {
        background: var(--sicoob-gray-300);
    }

    .btn-link {
        color: var(--sicoob-turquoise);
        text-decoration: none;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1.5rem;
        transition: color 0.2s;
    }

    .btn-link:hover {
        color: var(--sicoob-dark-green);
    }

    .back-to-login {
        margin-top: 1.5rem;
        text-align: center;
    }

    #timer {
        font-weight: 600;
        color: var(--sicoob-turquoise);
    }
    </style>

    <script>
    // Timer para expiração do código
    function startTimer(duration, display) {
        let timer = duration;
        const interval = setInterval(function () {
            const minutes = parseInt(timer / 60, 10);
            const seconds = parseInt(timer % 60, 10);

            display.textContent = minutes + ":" + (seconds < 10 ? "0" : "") + seconds;

            if (--timer < 0) {
                clearInterval(interval);
                display.textContent = "Expirado";
                display.style.color = "#dc3545";
            }
        }, 1000);
    }

    window.onload = function () {
        const timerDisplay = document.getElementById('timer');
        if (timerDisplay) {
            startTimer(300, timerDisplay);
        }

        // Formata o input do código automaticamente
        const codeInput = document.querySelector('.code-input');
        if (codeInput) {
            codeInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/[^0-9]/g, '');
            });
        }
    };
    </script>
</body>
</html>