<?php
require_once 'config/config.php';
require_once 'includes/auth.php';

try {
    // Verifica se as tabelas existem
    $tables = $pdo->query("SHOW TABLES LIKE 'campanha%'")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Status das tabelas:\n";
    
    // Verifica cada tabela esperada
    $expected_tables = ['campanha_users', 'campanha_vendas'];
    foreach ($expected_tables as $table) {
        if (in_array($table, $tables)) {
            echo "✓ Tabela {$table} existe\n";
            
            // Mostra a estrutura da tabela
            echo "\nEstrutura da tabela {$table}:\n";
            foreach ($pdo->query("DESCRIBE {$table}")->fetchAll(PDO::FETCH_ASSOC) as $column) {
                echo "  - {$column['Field']} ({$column['Type']})\n";
            }
            echo "\n";
        } else {
            echo "✗ Tabela {$table} não existe\n";
        }
    }
    
    // Se alguma tabela não existe, tenta criar
    if (count($tables) < count($expected_tables)) {
        echo "\nCriando tabelas ausentes...\n";
        setup_database();
        echo "Tabelas criadas com sucesso!\n";
    }
    
} catch (PDOException $e) {
    echo "Erro ao verificar tabelas: " . $e->getMessage() . "\n";
} 