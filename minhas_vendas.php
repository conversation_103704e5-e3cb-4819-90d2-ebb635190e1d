<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Verifica se o usuário está autenticado
check_auth();

// Obtém o usuário atual
$usuario = $_SESSION['user_id'];

// Busca o desafio ativo atual
$stmt = $pdo->prepare("
    SELECT id FROM desafios 
    WHERE status = 'ativo' 
    AND data_inicio <= CURDATE()
    AND (
        data_fim > CURDATE()
        OR (
            data_fim = CURDATE()
            AND (
                horario_fim IS NULL 
                OR TIME(horario_fim) > CURRENT_TIME()
            )
        )
    )
");
$stmt->execute();
$desafio = $stmt->fetch();

if (!$desafio) {
    $_SESSION['error_message'] = "Não há desafio ativo no momento.";
    header('Location: index.php');
    exit();
}

$desafio_id = $desafio['id'];

// Query para buscar as vendas do usuário no desafio atual
$query = "SELECT 
    v.id,
    v.cpf,
    a.nome,
    v.produto,
    v.valor,
    v.data_venda,
    v.data_atualizacao,
    v.pa
FROM campanha_vendas v
JOIN associados a ON v.cpf = a.cpf
WHERE v.usuario = ? 
AND v.desafio_id = ?
ORDER BY v.data_venda DESC";

$stmt = $pdo->prepare($query);
$stmt->execute([$usuario, $desafio_id]);
$vendas = $stmt->fetchAll();

// Lista de produtos para exibição
$produtos = [
    'CREDITO_PESSOAL' => 'Crédito Pessoal',
    'CHEQUE_ESPECIAL' => 'Cheque Especial',
    'CARTAO_CREDITO' => 'Cartão de Crédito',
    'DEBITO_AUTOMATICO' => 'Débito Automático'
];
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minhas Vendas - Campanha Sicoob</title>
    <link rel="icon" type="image/png" href="assets/images/icon.png">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/table.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <img src="assets/images/logo1.png" alt="Sicoob" class="logo">
            <?php include 'includes/menu.php'; ?>
        </div>
    </header>

    <main class="container">
        <h1>Minhas Vendas</h1>
        
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="success-message">
                <?php 
                echo $_SESSION['success_message'];
                unset($_SESSION['success_message']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="error-message">
                <?php 
                echo $_SESSION['error_message'];
                unset($_SESSION['error_message']);
                ?>
            </div>
        <?php endif; ?>

        <div class="content">
            <div class="table-container">
                <div class="table-header">
                    <h2><i class="fas fa-shopping-cart"></i> Vendas Realizadas</h2>
                    <div class="table-toolbar">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="searchInput" placeholder="Buscar..." class="form-control">
                        </div>
                        <span class="total-records">
                            <i class="fas fa-chart-bar"></i>
                            Total: <?php echo count($vendas); ?> vendas
                        </span>
                    </div>
                </div>

                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>CPF</th>
                                <th>Nome</th>
                                <th>PA</th>
                                <th>Produto</th>
                                <th>Valor</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($vendas as $venda): ?>
                                <tr>
                                    <td>
                                        <div><?php echo date('d/m/Y H:i', strtotime($venda['data_venda'])); ?></div>
                                        <?php if ($venda['data_atualizacao']): ?>
                                            <div class="text-muted">
                                                <i class="fas fa-edit"></i>
                                                Editado em: <?php echo date('d/m/Y H:i', strtotime($venda['data_atualizacao'])); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo mask_cpf($venda['cpf']); ?></td>
                                    <td><?php echo htmlspecialchars($venda['nome']); ?></td>
                                    <td class="text-center"><?php echo htmlspecialchars($venda['pa']); ?></td>
                                    <td>
                                        <span class="product-badge">
                                            <i class="fas <?php echo get_product_icon($venda['produto']); ?>"></i>
                                            <?php echo $produtos[$venda['produto']]; ?>
                                        </span>
                                    </td>
                                    <td class="currency">
                                        <?php if ($venda['produto'] === 'DEBITO_AUTOMATICO'): ?>
                                            <span class="status status-sim">Ativo</span>
                                        <?php else: ?>
                                            R$ <?php echo number_format($venda['valor'], 2, ',', '.'); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td class="actions-cell">
                                        <button onclick="editarVenda(<?php echo htmlspecialchars(json_encode($venda)); ?>)" 
                                                class="btn btn-secondary btn-sm">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button onclick="excluirVenda(<?php echo $venda['id']; ?>)" 
                                                class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                            Excluir
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal de Edição -->
    <div id="modal-edicao" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Venda</h3>
                <p class="modal-subtitle">Altere os dados da venda</p>
            </div>
            <form id="form-edicao" method="POST" action="editar_venda.php">
                <input type="hidden" name="venda_id" id="edit-venda-id">
                <input type="hidden" name="produto" id="edit-produto">
                
                <div class="form-group">
                    <label>CPF:</label>
                    <input type="text" id="edit-cpf" disabled class="form-control">
                </div>

                <div class="form-group">
                    <label>Nome:</label>
                    <input type="text" id="edit-nome" disabled class="form-control">
                </div>

                <div class="form-group">
                    <label>Produto:</label>
                    <input type="text" id="edit-produto-nome" disabled class="form-control">
                </div>

                <div class="form-group" id="edit-valor-group">
                    <label for="valor">Valor:</label>
                    <div class="input-currency">
                        <span class="currency-symbol">R$</span>
                        <input type="number" name="valor" id="edit-valor" class="form-control" step="0.01" required>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" onclick="fecharModalEdicao()" class="btn btn-secondary">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar</button>
                </div>
            </form>
        </div>
    </div>

    <script>
    // Função para editar venda
    function editarVenda(venda) {
        document.getElementById('edit-venda-id').value = venda.id;
        document.getElementById('edit-cpf').value = venda.cpf;
        document.getElementById('edit-nome').value = venda.nome;
        document.getElementById('edit-produto').value = venda.produto;
        document.getElementById('edit-produto-nome').value = produtos[venda.produto];
        
        const valorGroup = document.getElementById('edit-valor-group');
        const valorInput = document.getElementById('edit-valor');

        if (venda.produto === 'DEBITO_AUTOMATICO') {
            valorGroup.style.display = 'none';
            valorInput.value = '1';
            valorInput.removeAttribute('required');
        } else {
            valorGroup.style.display = 'block';
            valorInput.value = venda.valor;
            valorInput.setAttribute('required', 'required');
        }

        document.getElementById('modal-edicao').classList.add('show');
    }

    // Função para excluir venda
    function excluirVenda(id) {
        if (confirm('Tem certeza que deseja excluir esta venda?')) {
            window.location.href = `excluir_venda.php?id=${id}`;
        }
    }

    function fecharModalEdicao() {
        document.getElementById('modal-edicao').classList.remove('show');
    }

    // Produtos disponíveis
    const produtos = <?php echo json_encode($produtos); ?>;

    // Adiciona a funcionalidade de busca
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchText = this.value.toLowerCase();
        const table = document.querySelector('table');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length; j++) {
                const cell = cells[j];
                if (cell.textContent.toLowerCase().indexOf(searchText) > -1) {
                    found = true;
                    break;
                }
            }

            row.style.display = found ? '' : 'none';
        }
    });

    // Fecha o modal se clicar fora dele
    window.onclick = function(event) {
        const modal = document.getElementById('modal-edicao');
        if (event.target == modal) {
            modal.classList.remove('show');
        }
    }
    </script>

    <style>
    .product-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        background: var(--sicoob-gray-100);
        border-radius: var(--radius-sm);
        color: var(--sicoob-dark-green);
        font-weight: 500;
    }

    .product-badge i {
        color: var(--sicoob-turquoise);
    }

    .btn-danger {
        background: #dc3545;
        color: white;
    }

    .btn-danger:hover {
        background: #c82333;
    }

    .actions-cell {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .actions-cell .btn {
        padding: 0.25rem 0.5rem;
    }
    </style>
</body>
</html> 