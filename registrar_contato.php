<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado
check_auth();

// Verifica se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit();
}

// Obtém os dados do formulário
$cpf = $_POST['cpf'];
$usuario = $_SESSION['user_id'];

try {
    // Verifica se já existe um contato registrado para este CPF
    $stmt = $pdo->prepare("SELECT id FROM campanha_contatos WHERE cpf = ?");
    $stmt->execute([$cpf]);
    $contato_existente = $stmt->fetch();

    if (!$contato_existente) {
        // Registra o novo contato
        $stmt = $pdo->prepare("INSERT INTO campanha_contatos (cpf, usuario) VALUES (?, ?)");
        $stmt->execute([$cpf, $usuario]);
        echo json_encode(['success' => true, 'message' => 'Contato registrado com sucesso!']);
    } else {
        // Remove o registro de contato
        $stmt = $pdo->prepare("DELETE FROM campanha_contatos WHERE cpf = ?");
        $stmt->execute([$cpf]);
        echo json_encode(['success' => true, 'message' => 'Registro de contato removido!']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 