<?php
require_once 'config/config.php';
require 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;

// Parâmetros da URL
$desafio_id = isset($_GET['desafio']) ? (int)$_GET['desafio'] : null;
$tipo = isset($_GET['tipo']) ? $_GET['tipo'] : 'usuarios'; // usuarios ou pas
$produto = isset($_GET['produto']) ? $_GET['produto'] : 'TOTAL';

// Busca informações do desafio
$stmt = $pdo->prepare("SELECT nome, tipo_ranking FROM desafios WHERE id = ?");
$stmt->execute([$desafio_id]);
$desafio = $stmt->fetch();

if (!$desafio) {
    die('Desafio não encontrado');
}

// Configuração da query baseada no tipo de ranking
if ($desafio['tipo_ranking'] === 'pontos') {
    $query = "
        SELECT 
            " . ($tipo === 'usuarios' ? 'v.usuario' : 'v.pa') . " as nome,
            SUM(COALESCE(dp.pontos, 0)) as total_pontos,
            COUNT(*) as total_vendas
        FROM campanha_vendas v
        LEFT JOIN desafio_pontuacoes dp ON dp.desafio_id = v.desafio_id AND dp.produto = v.produto
        WHERE v.desafio_id = ?
        GROUP BY " . ($tipo === 'usuarios' ? 'v.usuario' : 'v.pa') . "
        ORDER BY total_pontos DESC";
    
    $params = [$desafio_id];
} else {
    $query = "
        SELECT 
            " . ($tipo === 'usuarios' ? 'v.usuario' : 'v.pa') . " as nome,
            SUM(v.valor) as valor_total,
            COUNT(*) as total_vendas
        FROM campanha_vendas v
        WHERE v.desafio_id = ? AND v.produto = ?
        GROUP BY " . ($tipo === 'usuarios' ? 'v.usuario' : 'v.pa') . "
        ORDER BY valor_total DESC";
    
    $params = [$desafio_id, $produto];
}

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$dados = $stmt->fetchAll();

// Criar nova planilha
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Configurar cabeçalho
$sheet->setCellValue('A1', 'Posição');
$sheet->setCellValue('B1', $tipo === 'usuarios' ? 'Usuário' : 'PA');
$sheet->setCellValue('C1', $desafio['tipo_ranking'] === 'pontos' ? 'Pontos' : 'Valor Total');
$sheet->setCellValue('D1', 'Total de Vendas');

// Estilo do cabeçalho
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['argb' => 'FFFFFFFF'],
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['argb' => 'FF003641'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['argb' => 'FF000000'],
        ],
    ],
];

$sheet->getStyle('A1:D1')->applyFromArray($headerStyle);

// Preencher dados
$row = 2;
foreach ($dados as $pos => $item) {
    $sheet->setCellValue('A' . $row, ($pos + 1) . 'º');
    $sheet->setCellValue('B' . $row, $item['nome']);
    
    if ($desafio['tipo_ranking'] === 'pontos') {
        $sheet->setCellValue('C' . $row, $item['total_pontos']);
    } else {
        $sheet->setCellValue('C' . $row, $item['valor_total']);
        $sheet->getStyle('C' . $row)->getNumberFormat()->setFormatCode('R$ #,##0.00');
    }
    
    $sheet->setCellValue('D' . $row, $item['total_vendas']);
    
    // Estilo das linhas de dados
    $sheet->getStyle('A' . $row . ':D' . $row)->applyFromArray([
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['argb' => 'FF000000'],
            ],
        ],
    ]);
    
    // Cor alternada nas linhas
    if ($row % 2 == 0) {
        $sheet->getStyle('A' . $row . ':D' . $row)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setARGB('FFF5F5F5');
    }
    
    $row++;
}

// Calcular totais
$lastRow = $row - 1;
$sheet->setCellValue('A' . $row, 'Total Geral:');
$sheet->mergeCells('A' . $row . ':B' . $row);
$sheet->setCellValue('C' . $row, '=SUM(C2:C' . $lastRow . ')');
$sheet->setCellValue('D' . $row, '=SUM(D2:D' . $lastRow . ')');

// Estilo da linha de total
$totalStyle = [
    'font' => [
        'bold' => true,
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['argb' => 'FFE0E0E0'],
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['argb' => 'FF000000'],
        ],
    ],
];

$sheet->getStyle('A' . $row . ':D' . $row)->applyFromArray($totalStyle);
if ($desafio['tipo_ranking'] !== 'pontos') {
    $sheet->getStyle('C' . $row)->getNumberFormat()->setFormatCode('R$ #,##0.00');
}

// Ajustar largura das colunas
$sheet->getColumnDimension('A')->setWidth(10);
$sheet->getColumnDimension('B')->setWidth(30);
$sheet->getColumnDimension('C')->setWidth(15);
$sheet->getColumnDimension('D')->setWidth(15);

// Nome do arquivo
$filename = 'Ranking_' . 
           ($tipo === 'usuarios' ? 'Usuarios' : 'PAs') . '_' . 
           str_replace(' ', '_', $desafio['nome']) . '_' . 
           date('Y-m-d_H-i-s') . '.xlsx';

// Cabeçalhos para download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

// Gerar arquivo Excel
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit; 