<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado
check_auth();

// Verifica se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit();
}

// Obtém os dados do formulário
$venda_id = $_POST['venda_id'];
$produto = $_POST['produto'];
$valor = $produto === 'DEBITO_AUTOMATICO' ? 1 : floatval($_POST['valor']);
$is_admin = isset($_POST['admin']) && $_POST['admin'] === '1';

try {
    // Inicia a transação
    $pdo->beginTransaction();

    // Busca a venda e o desafio
    $stmt = $pdo->prepare("
        SELECT v.usuario, v.pa, v.desafio_id, d.status, d.data_inicio, d.data_fim 
        FROM campanha_vendas v
        JOIN desafios d ON v.desafio_id = d.id
        WHERE v.id = ?
    ");
    $stmt->execute([$venda_id]);
    $venda = $stmt->fetch();

    if (!$venda) {
        throw new Exception("Venda não encontrada.");
    }

    // Verifica se o usuário tem permissão para editar a venda
    if (!$is_admin && $venda['usuario'] !== $_SESSION['user_id']) {
        throw new Exception("Você não tem permissão para editar esta venda.");
    }

    // Verifica se o desafio está ativo e dentro do período
    if ($venda['status'] !== 'ativo' || strtotime($venda['data_fim']) < time() || strtotime($venda['data_inicio']) > time()) {
        throw new Exception("Não é possível editar vendas de desafios inativos ou fora do período.");
    }

    // Atualiza a venda
    $stmt = $pdo->prepare("UPDATE campanha_vendas SET valor = ?, data_atualizacao = NOW() WHERE id = ?");
    $stmt->execute([$valor, $venda_id]);

    // Confirma a transação
    $pdo->commit();

    // Redireciona com mensagem de sucesso
    $_SESSION['success_message'] = "Venda atualizada com sucesso!";
    header('Location: ' . ($is_admin ? 'admin/vendas.php' : 'minhas_vendas.php'));
    exit();

} catch (Exception $e) {
    // Desfaz a transação em caso de erro
    $pdo->rollBack();
    
    // Redireciona com mensagem de erro
    $_SESSION['error_message'] = $e->getMessage();
    header('Location: ' . ($is_admin ? 'admin/vendas.php' : 'minhas_vendas.php'));
    exit();
} 