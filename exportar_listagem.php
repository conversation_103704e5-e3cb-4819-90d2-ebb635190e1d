<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Verifica autenticação
check_auth();

// Obtém o PA do usuário logado
$pa = get_user_pa();
$is_admin = $_SESSION['is_admin'];

// Obtém o termo de busca
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Query para buscar todos os registros (sem paginação para exportação)
$query = "SELECT 
    a.nome,
    a.cpf,
    a.telefone,
    a.pa,
    a.renda,
    a.data_aprovacao,
    a.limite as 'Crédito Pessoal Disponível',
    a.limite_uso as 'Crédito Pessoal em Uso',
    a.chespecial as cheque_especial,
    a.chespecial_uso as cheque_especial_uso,
    a.limite_cartao,
    a.debito_automatico as 'Débito Automático',
    UPPER(a.situacao_cartao) as 'Situação Conta Cartão'
FROM associados a
WHERE a.status IN (3, 8)";

$params = [];

if (!$is_admin && $pa) {
    $query .= " AND LPAD(a.pa, 2, '0') = ?";
    $params[] = str_pad($pa, 2, '0', STR_PAD_LEFT);
}

// Adiciona condição de busca
if ($search) {
    $query .= " AND (a.nome LIKE ? OR a.cpf LIKE ? OR a.pa LIKE ? OR a.telefone LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

$query .= " ORDER BY a.pa, a.data_aprovacao DESC";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$aprovados = $stmt->fetchAll();

// Cria uma nova planilha
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Define o título da planilha
$sheet->setTitle('Cadastros Aprovados');

// Cabeçalhos
$headers = [
    'A1' => 'CPF',
    'B1' => 'Nome',
    'C1' => 'Telefone',
    'D1' => 'PA',
    'E1' => 'Renda',
    'F1' => 'Data Aprovação',
    'G1' => 'Crédito Pessoal Disponível',
    'H1' => 'Crédito Pessoal em Uso',
    'I1' => 'Cheque Especial Disponível',
    'J1' => 'Cheque Especial em Uso',
    'K1' => 'Limite Cartão',
    'L1' => 'Débito Automático',
    'M1' => 'Situação Conta Cartão'
];

// Adiciona os cabeçalhos
foreach ($headers as $cell => $header) {
    $sheet->setCellValue($cell, $header);
}

// Estiliza o cabeçalho
$headerStyle = [
    'font' => [
        'bold' => true,
        'color' => ['rgb' => 'FFFFFF']
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => '003641'] // Verde escuro Sicoob
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER
    ],
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => '000000']
        ]
    ]
];

$sheet->getStyle('A1:M1')->applyFromArray($headerStyle);

// Adiciona os dados
$row = 2;
foreach ($aprovados as $aprovado) {
    $sheet->setCellValue('A' . $row, mask_cpf($aprovado['cpf']));
    $sheet->setCellValue('B' . $row, $aprovado['nome']);
    $sheet->setCellValue('C' . $row, $aprovado['telefone']);
    $sheet->setCellValue('D' . $row, $aprovado['pa']);
    $sheet->setCellValue('E' . $row, 'R$ ' . number_format($aprovado['renda'], 2, ',', '.'));
    $sheet->setCellValue('F' . $row, $aprovado['data_aprovacao'] ? date('d/m/Y H:i', strtotime($aprovado['data_aprovacao'])) : '-');
    $sheet->setCellValue('G' . $row, 'R$ ' . number_format($aprovado['Crédito Pessoal Disponível'], 2, ',', '.'));
    $sheet->setCellValue('H' . $row, 'R$ ' . number_format($aprovado['Crédito Pessoal em Uso'], 2, ',', '.'));
    $sheet->setCellValue('I' . $row, 'R$ ' . number_format($aprovado['cheque_especial'], 2, ',', '.'));
    $sheet->setCellValue('J' . $row, 'R$ ' . number_format($aprovado['cheque_especial_uso'], 2, ',', '.'));
    $sheet->setCellValue('K' . $row, 'R$ ' . number_format($aprovado['limite_cartao'], 2, ',', '.'));
    $sheet->setCellValue('L' . $row, $aprovado['Débito Automático']);
    $sheet->setCellValue('M' . $row, $aprovado['Situação Conta Cartão']);
    
    $row++;
}

// Estiliza as células de dados
$dataStyle = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            'color' => ['rgb' => 'CCCCCC']
        ]
    ],
    'alignment' => [
        'vertical' => Alignment::VERTICAL_CENTER
    ]
];

if ($row > 2) {
    $sheet->getStyle('A2:M' . ($row - 1))->applyFromArray($dataStyle);
}

// Ajusta a largura das colunas
$sheet->getColumnDimension('A')->setWidth(15); // CPF
$sheet->getColumnDimension('B')->setWidth(30); // Nome
$sheet->getColumnDimension('C')->setWidth(15); // Telefone
$sheet->getColumnDimension('D')->setWidth(8);  // PA
$sheet->getColumnDimension('E')->setWidth(15); // Renda
$sheet->getColumnDimension('F')->setWidth(18); // Data Aprovação
$sheet->getColumnDimension('G')->setWidth(20); // Crédito Pessoal Disponível
$sheet->getColumnDimension('H')->setWidth(20); // Crédito Pessoal em Uso
$sheet->getColumnDimension('I')->setWidth(20); // Cheque Especial Disponível
$sheet->getColumnDimension('J')->setWidth(20); // Cheque Especial em Uso
$sheet->getColumnDimension('K')->setWidth(15); // Limite Cartão
$sheet->getColumnDimension('L')->setWidth(15); // Débito Automático
$sheet->getColumnDimension('M')->setWidth(20); // Situação Conta Cartão

// Define o nome do arquivo
$filename = 'cadastros_aprovados_' . date('Y-m-d_H-i-s') . '.xlsx';

// Configura os headers para download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

// Cria o writer e salva o arquivo
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');

// Limpa a memória
$spreadsheet->disconnectWorksheets();
unset($spreadsheet);
exit();
?>
