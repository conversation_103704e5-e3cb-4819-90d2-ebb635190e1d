<?php
session_start();
require_once '../config/config.php';
require_once '../includes/auth.php';

// Verifica se o usuário está autenticado e é administrador
check_auth();
if (!$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['error' => 'Acesso negado']);
    exit();
}

// Verifica se o ID foi fornecido
if (!isset($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'ID não fornecido']);
    exit();
}

try {
    // Busca os dados do desafio
    $stmt = $pdo->prepare("SELECT * FROM desafios WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $desafio = $stmt->fetch();

    if (!$desafio) {
        http_response_code(404);
        echo json_encode(['error' => 'Desafio não encontrado']);
        exit();
    }

    // Formata as datas para o formato esperado pelo input type="date"
    $desafio['data_inicio'] = date('Y-m-d', strtotime($desafio['data_inicio']));
    $desafio['data_fim'] = date('Y-m-d', strtotime($desafio['data_fim']));

    // Retorna os dados do desafio
    echo json_encode($desafio);

} catch (Exception $e) {
    error_log("Erro ao buscar desafio: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao buscar dados do desafio']);
} 