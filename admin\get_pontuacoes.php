<?php
session_start();
require_once '../config/config.php';
require_once '../includes/auth.php';

// Verifica se é admin
check_admin();

// Verifica se o ID do desafio foi fornecido
if (!isset($_GET['desafio_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'ID do desafio não fornecido']);
    exit;
}

$desafio_id = (int)$_GET['desafio_id'];

try {
    // Busca as pontuações do desafio
    $stmt = $pdo->prepare("SELECT produto, pontos FROM desafio_pontuacoes WHERE desafio_id = ?");
    $stmt->execute([$desafio_id]);
    $pontuacoes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($pontuacoes);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao buscar pontuações: ' . $e->getMessage()]);
} 