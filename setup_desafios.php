<?php
session_start(); // Adicionar in<PERSON><PERSON>
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado e é administrador
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    $_SESSION['error_message'] = "Acesso negado. Você precisa estar logado como administrador.";
    header('Location: login.php');
    exit();
}

// Função para exibir mensagens
function show_message($message, $type = 'success') {
    $bg_color = $type === 'success' ? '#dff0d8' : '#f2dede';
    $text_color = $type === 'success' ? '#3c763d' : '#a94442';
    echo "<div style='background-color: {$bg_color}; color: {$text_color}; padding: 15px; margin: 20px; border-radius: 4px;'>";
    echo $message;
    echo "<br><a href='index.php'>Voltar para a página inicial</a>";
    echo "</div>";
}

// Array para armazenar logs de operações
$log = [];

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Cria a tabela de desafios
    $sql = "CREATE TABLE IF NOT EXISTS desafios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        descricao TEXT,
        data_inicio DATE NOT NULL,
        data_fim DATE NOT NULL,
        meta_vendas INT NOT NULL,
        tipo_ranking ENUM('vendas', 'pontos') DEFAULT 'vendas',
        status ENUM('ativo', 'inativo', 'finalizado') DEFAULT 'inativo',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "Tabela 'desafios' criada com sucesso!\n";

    // Cria a tabela de vendas
    $sql = "CREATE TABLE IF NOT EXISTS vendas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        desafio_id INT NOT NULL,
        username VARCHAR(50) NOT NULL,
        pa VARCHAR(10) NOT NULL,
        produto VARCHAR(255) NOT NULL,
        quantidade INT NOT NULL,
        data_venda DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (desafio_id) REFERENCES desafios(id)
    )";
    $pdo->exec($sql);
    echo "Tabela 'vendas' criada com sucesso!\n";

    // Cria a tabela de usuários
    $sql = "CREATE TABLE IF NOT EXISTS campanha_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        pa VARCHAR(10) NOT NULL,
        is_admin TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "Tabela 'campanha_users' criada com sucesso!\n";

    // Cria a tabela de pontuações dos produtos
    $sql = "CREATE TABLE IF NOT EXISTS desafio_pontuacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        desafio_id INT NOT NULL,
        produto VARCHAR(50) NOT NULL,
        pontos INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (desafio_id) REFERENCES desafios(id),
        UNIQUE KEY unique_desafio_produto (desafio_id, produto)
    )";
    $pdo->exec($sql);
    echo "Tabela 'desafio_pontuacoes' criada com sucesso!\n";

    // 1. Criar backups das tabelas existentes
    $log[] = "Criando backup das tabelas...";
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_vendas_backup AS SELECT * FROM campanha_vendas");
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_users_backup AS SELECT * FROM campanha_users");
    
    // 2. Criar nova tabela de desafios
    $log[] = "Criando tabela de desafios...";
    $pdo->exec("CREATE TABLE IF NOT EXISTS desafios (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nome VARCHAR(255) NOT NULL,
        data_inicio DATE NOT NULL,
        data_fim DATE NOT NULL,
        status ENUM('ativo', 'inativo', 'finalizado') DEFAULT 'inativo',
        meta_vendas DECIMAL(10,2),
        regras TEXT,
        produtos_permitidos TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");

    // 3. Criar tabela de configurações de desafio
    $log[] = "Criando tabela de configurações...";
    $pdo->exec("CREATE TABLE IF NOT EXISTS desafio_configuracoes (
        id INT PRIMARY KEY AUTO_INCREMENT,
        desafio_id INT NOT NULL,
        tipo_configuracao VARCHAR(50) NOT NULL,
        valor TEXT NOT NULL,
        FOREIGN KEY (desafio_id) REFERENCES desafios(id)
    ) ENGINE=InnoDB");

    // 4. Verificar se a coluna desafio_id já existe
    $log[] = "Verificando estrutura da tabela de vendas...";
    $columns = $pdo->query("SHOW COLUMNS FROM campanha_vendas")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('desafio_id', $columns)) {
        $log[] = "Adicionando coluna desafio_id...";
        $pdo->exec("ALTER TABLE campanha_vendas 
                    ADD COLUMN desafio_id INT NOT NULL DEFAULT 1,
                    ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
    }

    // 5. Verificar se já existe um desafio inicial
    $log[] = "Verificando desafio inicial...";
    $exists = $pdo->query("SELECT COUNT(*) FROM desafios WHERE id = 1")->fetchColumn();
    
    if (!$exists) {
        $log[] = "Criando desafio inicial...";
        $stmt = $pdo->prepare("INSERT INTO desafios (nome, data_inicio, data_fim, status) 
                              VALUES (?, ?, ?, ?)");
        $stmt->execute(['Desafio 2024/1', '2024-01-01', '2024-12-31', 'finalizado']);
    }

    // 6. Atualizar vendas existentes
    $log[] = "Atualizando registros de vendas...";
    $pdo->exec("UPDATE campanha_vendas SET desafio_id = 1 WHERE desafio_id = 0");

    // 7. Adicionar foreign key se não existir
    $log[] = "Adicionando foreign key...";
    try {
        // Primeiro, verifica se a foreign key já existe
        $fks = $pdo->query("
            SELECT COUNT(*) 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE()
            AND TABLE_NAME = 'campanha_vendas' 
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            AND CONSTRAINT_NAME LIKE '%desafio_id%'
        ")->fetchColumn();

        if ($fks == 0) {
            $pdo->exec("ALTER TABLE campanha_vendas 
                       ADD CONSTRAINT fk_desafio_id 
                       FOREIGN KEY (desafio_id) 
                       REFERENCES desafios(id)");
            $log[] = "Foreign key adicionada com sucesso.";
        } else {
            $log[] = "Foreign key já existe.";
        }
    } catch (PDOException $e) {
        $log[] = "Aviso: Não foi possível adicionar a foreign key. Erro: " . $e->getMessage();
    }
    
    // Log de sucesso
    $log[] = "Migração concluída com sucesso!";
    show_message(implode("<br>", $log), 'success');

} catch (Exception $e) {
    // Log do erro
    error_log("Erro na migração para múltiplos desafios: " . $e->getMessage());
    
    // Exibe mensagem de erro
    show_message("Erro durante a migração: " . $e->getMessage() . "\n\nLogs até o momento:\n" . implode("<br>", $log), 'error');
} 