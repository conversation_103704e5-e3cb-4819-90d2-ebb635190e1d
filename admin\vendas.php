<?php
session_start();
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Verifica se é admin
check_admin();

// Busca todos os desafios
$stmt = $pdo->query("SELECT id, nome, status, data_inicio, data_fim FROM desafios ORDER BY data_inicio DESC");
$desafios = $stmt->fetchAll();

// Obtém o desafio selecionado (default para o primeiro desafio)
$desafio_id = isset($_GET['desafio']) ? (int)$_GET['desafio'] : ($desafios[0]['id'] ?? null);

// Busca informações do desafio atual
$stmt = $pdo->prepare("SELECT tipo_ranking FROM desafios WHERE id = ?");
$stmt->execute([$desafio_id]);
$desafio_atual = $stmt->fetch();
$tipo_ranking = $desafio_atual['tipo_ranking'] ?? 'vendas';

// Obtém os filtros
$filtro_usuario = isset($_GET['usuario']) ? $_GET['usuario'] : '';
$filtro_cpf = isset($_GET['cpf']) ? preg_replace('/[^0-9]/', '', $_GET['cpf']) : '';
$filtro_produto = isset($_GET['produto']) ? $_GET['produto'] : '';

// Lista de produtos para exibição
$produtos = [
    'CREDITO_PESSOAL' => [
        'nome' => 'Crédito Pessoal',
        'icone' => 'fa-money-bill-wave',
        'cor' => '#00AE9D'
    ],
    'CHEQUE_ESPECIAL' => [
        'nome' => 'Cheque Especial',
        'icone' => 'fa-money-check',
        'cor' => '#003641'
    ],
    'CARTAO_CREDITO' => [
        'nome' => 'Cartão de Crédito',
        'icone' => 'fa-credit-card',
        'cor' => '#C9D200'
    ],
    'DEBITO_AUTOMATICO' => [
        'nome' => 'Débito Automático',
        'icone' => 'fa-sync',
        'cor' => '#7DB61C'
    ]
];

// Busca o volume de vendas por produto
$query_volume = "SELECT 
    produto,
    COUNT(*) as total_vendas,
    SUM(CASE WHEN produto != 'DEBITO_AUTOMATICO' THEN valor ELSE 0 END) as valor_total,
    SUM(CASE 
        WHEN produto = 'DEBITO_AUTOMATICO' THEN 1 
        ELSE 0 
    END) as total_debitos
FROM campanha_vendas 
WHERE desafio_id = ?
GROUP BY produto";

$stmt = $pdo->prepare($query_volume);
$stmt->execute([$desafio_id]);
$volumes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Organiza os volumes em um array associativo
$volumes_por_produto = [];
foreach ($volumes as $volume) {
    $volumes_por_produto[$volume['produto']] = $volume;
}

// Query base para buscar as vendas
$query = "SELECT 
    v.id,
    v.cpf,
    a.nome,
    v.produto,
    v.valor,
    v.data_venda,
    v.data_atualizacao,
    v.pa,
    v.usuario,
    d.nome as desafio_nome";

// Adiciona subconsulta para pontos se o desafio for por pontuação
if ($tipo_ranking === 'pontos') {
    $query .= ", COALESCE((
        SELECT dp.pontos 
        FROM desafio_pontuacoes dp 
        WHERE dp.desafio_id = v.desafio_id 
        AND dp.produto = v.produto
    ), 0) as pontos";
}

$query .= " FROM campanha_vendas v
    JOIN associados a ON v.cpf = a.cpf
    JOIN desafios d ON v.desafio_id = d.id
    WHERE v.desafio_id = ?";

$params = [$desafio_id];

// Aplica os filtros
if ($filtro_usuario) {
    $query .= " AND v.usuario LIKE ?";
    $params[] = "%$filtro_usuario%";
}
if ($filtro_cpf) {
    $query .= " AND v.cpf = ?";
    $params[] = $filtro_cpf;
}
if ($filtro_produto) {
    $query .= " AND v.produto = ?";
    $params[] = $filtro_produto;
}

$query .= " ORDER BY v.data_venda DESC";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$vendas = $stmt->fetchAll();

// Busca lista de usuários para o filtro
$stmt = $pdo->query("SELECT DISTINCT usuario FROM campanha_vendas ORDER BY usuario");
$usuarios = $stmt->fetchAll(PDO::FETCH_COLUMN);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Vendas - Campanha Sicoob</title>
    <link rel="icon" type="image/png" href="../assets/images/icon.png">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/table.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <img src="../assets/images/logo1.png" alt="Sicoob" class="logo">
            <?php include '../includes/menu.php'; ?>
        </div>
    </header>

    <main class="container">
        <h1>Gerenciar Vendas</h1>
        
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="success-message">
                <?php 
                echo $_SESSION['success_message'];
                unset($_SESSION['success_message']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="error-message">
                <?php 
                echo $_SESSION['error_message'];
                unset($_SESSION['error_message']);
                ?>
            </div>
        <?php endif; ?>

        <div class="content">
            <!-- Cards de Volume de Vendas -->
            <div class="volume-cards">
                <?php foreach ($produtos as $codigo => $produto): ?>
                    <?php 
                    $volume = $volumes_por_produto[$codigo] ?? [
                        'total_vendas' => 0,
                        'valor_total' => 0,
                        'total_debitos' => 0
                    ];
                    ?>
                    <div class="volume-card" style="border-left: 4px solid <?php echo $produto['cor']; ?>">
                        <div class="card-icon" style="background: <?php echo $produto['cor']; ?>20">
                            <i class="fas <?php echo $produto['icone']; ?>" style="color: <?php echo $produto['cor']; ?>"></i>
                        </div>
                        <div class="card-content">
                            <h3><?php echo $produto['nome']; ?></h3>
                            <div class="card-stats">
                                <div class="stat">
                                    <span class="stat-label">Total de Vendas</span>
                                    <span class="stat-value"><?php echo $volume['total_vendas']; ?></span>
                                </div>
                                <?php if ($codigo === 'DEBITO_AUTOMATICO'): ?>
                                    <div class="stat">
                                        <span class="stat-label">Ativações</span>
                                        <span class="stat-value"><?php echo $volume['total_debitos']; ?></span>
                                    </div>
                                <?php else: ?>
                                    <div class="stat">
                                        <span class="stat-label">Valor Total</span>
                                        <span class="stat-value">R$ <?php echo number_format($volume['valor_total'], 2, ',', '.'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="filters-section">
                <form method="GET" class="filters-form">
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="desafio">Desafio:</label>
                            <select name="desafio" id="desafio" class="form-control" onchange="this.form.submit()">
                                <?php foreach ($desafios as $desafio): ?>
                                    <option value="<?php echo $desafio['id']; ?>" 
                                            <?php echo $desafio['id'] == $desafio_id ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($desafio['nome']); ?> 
                                        (<?php echo date('d/m/Y', strtotime($desafio['data_inicio'])); ?> - 
                                         <?php echo date('d/m/Y', strtotime($desafio['data_fim'])); ?>)
                                        <?php echo $desafio['status'] === 'ativo' ? ' - Ativo' : ''; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group col-md-3">
                            <label for="usuario">Usuário:</label>
                            <select name="usuario" id="usuario" class="form-control">
                                <option value="">Todos os usuários</option>
                                <?php foreach ($usuarios as $user): ?>
                                    <option value="<?php echo $user; ?>" <?php echo $user === $filtro_usuario ? 'selected' : ''; ?>>
                                        <?php echo $user; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group col-md-2">
                            <label for="produto">Produto:</label>
                            <select name="produto" id="produto" class="form-control">
                                <option value="">Todos os produtos</option>
                                <?php foreach ($produtos as $codigo => $produto): ?>
                                    <option value="<?php echo $codigo; ?>" <?php echo $codigo === $filtro_produto ? 'selected' : ''; ?>>
                                        <?php echo $produto['nome']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group col-md-2">
                            <label for="cpf">CPF:</label>
                            <input type="text" name="cpf" id="cpf" class="form-control" 
                                   value="<?php echo $filtro_cpf; ?>" placeholder="Digite o CPF">
                        </div>

                        <div class="form-actions col-md-1">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i>
                            </button>
                            <a href="vendas.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <div class="table-container">
                <div class="table-header">
                    <h2><i class="fas fa-shopping-cart"></i> Vendas Registradas</h2>
                    <div class="table-toolbar">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="searchInput" placeholder="Buscar...">
                        </div>
                        <a href="export_vendas.php?desafio=<?php echo $desafio_id; ?>" class="btn btn-success" title="Exportar para Excel">
                            <i class="fas fa-file-excel"></i> Exportar Excel
                        </a>
                        <span class="total-records">
                            <i class="fas fa-chart-bar"></i>
                            Total: <?php echo count($vendas); ?> vendas
                        </span>
                    </div>
                </div>

                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>CPF</th>
                                <th>Nome</th>
                                <th>PA</th>
                                <th>Usuário</th>
                                <th>Produto</th>
                                <th>Valor</th>
                                <?php if ($tipo_ranking === 'pontos'): ?>
                                <th>Pontos</th>
                                <?php endif; ?>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($vendas as $venda): ?>
                                <tr>
                                    <td>
                                        <div><?php echo date('d/m/Y H:i', strtotime($venda['data_venda'])); ?></div>
                                        <?php if ($venda['data_atualizacao']): ?>
                                            <div class="text-muted">
                                                <i class="fas fa-edit"></i>
                                                Editado em: <?php echo date('d/m/Y H:i', strtotime($venda['data_atualizacao'])); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo mask_cpf($venda['cpf']); ?></td>
                                    <td><?php echo htmlspecialchars($venda['nome']); ?></td>
                                    <td class="text-center"><?php echo htmlspecialchars($venda['pa']); ?></td>
                                    <td><?php echo htmlspecialchars($venda['usuario']); ?></td>
                                    <td>
                                        <span class="product-badge">
                                            <i class="fas <?php echo get_product_icon($venda['produto']); ?>"></i>
                                            <?php echo $produtos[$venda['produto']]['nome']; ?>
                                        </span>
                                    </td>
                                    <td class="currency">
                                        <?php if ($venda['produto'] === 'DEBITO_AUTOMATICO'): ?>
                                            <span class="status status-sim">Ativo</span>
                                        <?php else: ?>
                                            R$ <?php echo number_format($venda['valor'], 2, ',', '.'); ?>
                                        <?php endif; ?>
                                    </td>
                                    <?php if ($tipo_ranking === 'pontos'): ?>
                                    <td class="text-center">
                                        <span class="badge badge-info">
                                            <?php echo $venda['pontos']; ?> pts
                                        </span>
                                    </td>
                                    <?php endif; ?>
                                    <td class="actions-cell">
                                        <button onclick="editarVenda(<?php echo htmlspecialchars(json_encode($venda)); ?>)" 
                                                class="btn btn-secondary btn-sm">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button onclick="excluirVenda(<?php echo $venda['id']; ?>)" 
                                                class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                            Excluir
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal de Edição -->
    <div id="modal-edicao" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Venda</h3>
                <p class="modal-subtitle">Altere os dados da venda</p>
            </div>
            <form id="form-edicao" method="POST" action="../editar_venda.php">
                <input type="hidden" name="venda_id" id="edit-venda-id">
                <input type="hidden" name="produto" id="edit-produto">
                <input type="hidden" name="admin" value="1">
                
                <div class="form-group">
                    <label>CPF:</label>
                    <input type="text" id="edit-cpf" disabled class="form-control">
                </div>

                <div class="form-group">
                    <label>Nome:</label>
                    <input type="text" id="edit-nome" disabled class="form-control">
                </div>

                <div class="form-group">
                    <label>Usuário:</label>
                    <input type="text" id="edit-usuario" disabled class="form-control">
                </div>

                <div class="form-group">
                    <label>Produto:</label>
                    <input type="text" id="edit-produto-nome" disabled class="form-control">
                </div>

                <div class="form-group" id="edit-valor-group">
                    <label for="valor">Valor:</label>
                    <div class="input-currency">
                        <span class="currency-symbol">R$</span>
                        <input type="number" name="valor" id="edit-valor" class="form-control" step="0.01" required>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" onclick="fecharModalEdicao()" class="btn btn-secondary">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar</button>
                </div>
            </form>
        </div>
    </div>

    <style>
    .volume-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .volume-card {
        background: white;
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        transition: transform 0.2s, box-shadow 0.2s;
        height: 100%;
    }

    .volume-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .card-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .card-icon i {
        font-size: 24px;
    }

    .card-content {
        flex: 1;
        min-width: 0;
    }

    .card-content h3 {
        margin: 0 0 0.5rem;
        color: var(--sicoob-dark-green);
        font-size: 1rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .card-stats {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .stat {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
    }

    .stat-label {
        color: var(--sicoob-gray-600);
        margin-right: 0.5rem;
    }

    .stat-value {
        font-weight: 600;
        color: var(--sicoob-dark-green);
        white-space: nowrap;
    }

    .filters-section {
        background: white;
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        margin-bottom: 2rem;
        box-shadow: var(--shadow-sm);
    }

    .form-row {
        display: flex;
        flex-wrap: nowrap;
        gap: 1rem;
        margin: 0;
        align-items: flex-end;
    }

    .col-md-1, .col-md-2, .col-md-3, .col-md-4 {
        padding: 0;
        position: relative;
    }

    @media (min-width: 768px) {
        .col-md-1 { flex: 0 0 8%; max-width: 8%; }
        .col-md-2 { flex: 0 0 15%; max-width: 15%; }
        .col-md-3 { flex: 0 0 25%; max-width: 25%; }
        .col-md-4 { flex: 0 0 35%; max-width: 35%; }
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 0;
    }

    .form-group label {
        font-size: 0.875rem;
        color: var(--sicoob-gray-600);
        margin-bottom: 0;
    }

    .form-control {
        height: 38px;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border: 1px solid var(--sicoob-gray-300);
        border-radius: var(--radius-md);
        width: 100%;
    }

    .form-actions {
        display: flex;
        gap: 0.5rem;
        height: 38px;
    }

    .form-actions .btn {
        width: 38px;
        height: 38px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .form-actions .btn i {
        font-size: 14px;
    }

    @media (max-width: 1200px) {
        .form-row {
            flex-wrap: wrap;
        }

        .col-md-4 { flex: 0 0 35%; max-width: 35%; }
        .col-md-3 { flex: 0 0 25%; max-width: 25%; }
        .col-md-2 { flex: 0 0 15%; max-width: 15%; }
        .col-md-1 { flex: 0 0 8%; max-width: 8%; }
    }

    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 1rem;
        }

        .col-md-1, .col-md-2, .col-md-3, .col-md-4 {
            flex: 0 0 100%;
            max-width: 100%;
        }

        .form-actions {
            justify-content: flex-start;
        }
    }

    .product-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        background: var(--sicoob-gray-100);
        border-radius: var(--radius-sm);
        color: var(--sicoob-dark-green);
        font-weight: 500;
    }

    .product-badge i {
        color: var(--sicoob-turquoise);
    }

    .btn-danger {
        background: #dc3545;
        color: white;
    }

    .btn-danger:hover {
        background: #c82333;
    }

    .actions-cell {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .actions-cell .btn {
        padding: 0.25rem 0.5rem;
    }

    .btn-success {
        background-color: var(--sicoob-green);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.2s;
    }

    .btn-success:hover {
        background-color: var(--sicoob-dark-green);
    }

    .btn-success i {
        font-size: 16px;
    }

    .table-toolbar {
        display: flex;
        align-items: center;
        gap: 16px;
    }
    </style>

    <script>
    // Função para editar venda
    function editarVenda(venda) {
        document.getElementById('edit-venda-id').value = venda.id;
        document.getElementById('edit-cpf').value = venda.cpf;
        document.getElementById('edit-nome').value = venda.nome;
        document.getElementById('edit-usuario').value = venda.usuario;
        document.getElementById('edit-produto').value = venda.produto;
        document.getElementById('edit-produto-nome').value = produtos[venda.produto]['nome'];
        
        const valorGroup = document.getElementById('edit-valor-group');
        const valorInput = document.getElementById('edit-valor');

        if (venda.produto === 'DEBITO_AUTOMATICO') {
            valorGroup.style.display = 'none';
            valorInput.value = '1';
            valorInput.removeAttribute('required');
        } else {
            valorGroup.style.display = 'block';
            valorInput.value = venda.valor;
            valorInput.setAttribute('required', 'required');
        }

        document.getElementById('modal-edicao').classList.add('show');
    }

    // Função para excluir venda
    function excluirVenda(id) {
        if (confirm('Tem certeza que deseja excluir esta venda?')) {
            window.location.href = `../excluir_venda.php?id=${id}&admin=1`;
        }
    }

    function fecharModalEdicao() {
        document.getElementById('modal-edicao').classList.remove('show');
    }

    // Produtos disponíveis
    const produtos = <?php echo json_encode($produtos); ?>;

    // Adiciona a funcionalidade de busca
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchText = this.value.toLowerCase();
        const table = document.querySelector('table');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length; j++) {
                const cell = cells[j];
                if (cell.textContent.toLowerCase().indexOf(searchText) > -1) {
                    found = true;
                    break;
                }
            }

            row.style.display = found ? '' : 'none';
        }
    });

    // Fecha o modal se clicar fora dele
    window.onclick = function(event) {
        const modal = document.getElementById('modal-edicao');
        if (event.target == modal) {
            modal.classList.remove('show');
        }
    }

    // Formata o CPF enquanto digita
    document.getElementById('cpf').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 11) value = value.slice(0, 11);
        if (value.length > 9) {
            value = value.replace(/^(\d{3})(\d{3})(\d{3})(\d{2}).*/, '$1.$2.$3-$4');
        } else if (value.length > 6) {
            value = value.replace(/^(\d{3})(\d{3})(\d{3}).*/, '$1.$2.$3');
        } else if (value.length > 3) {
            value = value.replace(/^(\d{3})(\d{3}).*/, '$1.$2');
        }
        e.target.value = value;
    });
    </script>
</body>
</html>