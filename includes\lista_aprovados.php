<?php
// Configuração da paginação
$items_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $items_per_page;

// Obtém o PA do usuário logado
$pa = get_user_pa();
$is_admin = $_SESSION['is_admin'];

// Obtém o termo de busca
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Query para contar total de registros
$count_query = "SELECT COUNT(*) as total FROM associados a WHERE a.status IN (3, 8)";
if (!$is_admin && $pa) {
    // Converte o PA do registro para 2 dígitos para comparação
    $count_query .= " AND LPAD(a.pa, 2, '0') = ?";
}

// Adiciona condição de busca
if ($search) {
    $count_query .= " AND (a.nome LIKE ? OR a.cpf LIKE ? OR a.pa LIKE ? OR a.telefone LIKE ?)";
}

$stmt = $pdo->prepare($count_query);
$params = [];

if (!$is_admin && $pa) {
    $params[] = normalize_pa($pa);
}

if ($search) {
    $search_param = "%{$search}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$stmt->execute($params);
$total_records = $stmt->fetch()['total'];
$total_pages = ceil($total_records / $items_per_page);

// Query para buscar aprovados com paginação
$query = "SELECT 
    a.nome,
    a.cpf,
    a.telefone,
    a.pa,
    a.renda,
    a.data_aprovacao,
    a.limite as 'Crédito Pessoal Disponível',
    a.limite_uso as 'Crédito Pessoal em Uso',
    a.chespecial as cheque_especial,
    a.chespecial_uso as cheque_especial_uso,
    a.limite_cartao,
    a.debito_automatico as 'Débito Automático',
    UPPER(a.situacao_cartao) as 'Situação Conta Cartão',
    GROUP_CONCAT(
        CONCAT(v.produto, ':', COALESCE(v.valor, 0))
        ORDER BY v.data_venda DESC
    ) as vendas_info,
    c.id as contato_id,
    c.data_contato,
    c.usuario as contato_usuario
FROM associados a
LEFT JOIN (
    SELECT cv.* 
    FROM campanha_vendas cv
    INNER JOIN desafios d ON cv.desafio_id = d.id 
    WHERE d.status = 'ativo' 
    AND d.data_inicio <= CURDATE()
    AND (
        d.data_fim > CURDATE()
        OR (
            d.data_fim = CURDATE()
            AND (
                d.horario_fim IS NULL 
                OR TIME(d.horario_fim) > CURRENT_TIME()
            )
        )
    )
) v ON a.cpf = v.cpf
LEFT JOIN campanha_contatos c ON a.cpf = c.cpf
WHERE a.status IN (3, 8)";

if (!$is_admin && $pa) {
    $query .= " AND LPAD(a.pa, 2, '0') = ?";
}

// Adiciona condição de busca
if ($search) {
    $query .= " AND (a.nome LIKE ? OR a.cpf LIKE ? OR a.pa LIKE ? OR a.telefone LIKE ?)";
}

$query .= " GROUP BY a.cpf, a.nome, a.telefone, a.pa, a.renda, a.data_aprovacao, a.limite, a.limite_uso, a.chespecial, a.chespecial_uso, a.limite_cartao, a.debito_automatico, a.situacao_cartao, c.id, c.data_contato, c.usuario";
$query .= " ORDER BY a.pa, a.data_aprovacao DESC LIMIT " . (int)$items_per_page . " OFFSET " . (int)$offset;

$stmt = $pdo->prepare($query);
$params = [];

if (!$is_admin && $pa) {
    $params[] = normalize_pa($pa);
}

if ($search) {
    $search_param = "%{$search}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$stmt->execute($params);
$aprovados = $stmt->fetchAll();

// Lista de produtos disponíveis para venda
$produtos = [
    'CREDITO_PESSOAL' => 'Crédito Pessoal',
    'CHEQUE_ESPECIAL' => 'Cheque Especial',
    'CARTAO_CREDITO' => 'Cartão de Crédito',
    'DEBITO_AUTOMATICO' => 'Débito Automático'
];
?>

<!-- Importa os estilos -->
<link rel="stylesheet" href="assets/css/table.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="table-container">
    <div class="table-header">
        <h2><i class="fas fa-check-circle"></i> CADASTROS APROVADOS PELO RPA</h2>
        <div class="table-toolbar">
            <div class="search-box">
                <form method="GET" class="search-form">
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-search"></i></span>
                        <input type="text" id="searchInput" name="search" placeholder="Buscar..." class="form-control" value="<?php echo htmlspecialchars($search); ?>">
                        <?php if (isset($_GET['page'])): ?>
                            <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page']); ?>">
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i> Buscar
                        </button>
                    </div>
                </form>
            </div>
            <span class="total-records"><i class="fas fa-users"></i> Total: <?php echo $total_records; ?> registros</span>
        </div>
    </div>
    
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>CPF</th>
                    <th>Nome</th>
                    <th>PA</th>
                    <th>Renda</th>
                    <th>Limites</th>
                    <th>Cartão</th>
                    <th class="header-multiline">Débito<br>Automático</th>
                    <th>Vendas</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($aprovados as $aprovado): ?>
                    <tr class="<?php echo $aprovado['contato_id'] ? 'contato-realizado' : ''; ?>">
                        <td>
                            <div><?php echo mask_cpf($aprovado['cpf']); ?></div>
                            <div class="text-muted"><?php echo htmlspecialchars($aprovado['telefone']); ?></div>
                        </td>
                        <td>
                            <div><?php echo htmlspecialchars($aprovado['nome']); ?></div>
                            <div class="text-muted">
                                <i class="fas fa-calendar"></i> 
                                <?php echo $aprovado['data_aprovacao'] ? date('d/m/Y H:i', strtotime($aprovado['data_aprovacao'])) : '-'; ?>
                            </div>
                        </td>
                        <td class="text-center"><?php echo htmlspecialchars($aprovado['pa']); ?></td>
                        <td class="currency">R$ <?php echo number_format($aprovado['renda'], 2, ',', '.'); ?></td>
                        <td>
                            <div class="limits-info">
                                <div class="limit-item">
                                    <span class="limit-label"><strong>Crédito Pessoal:</strong></span>
                                    <div class="limit-values">
                                        <div class="available" title="Disponível">
                                            <i class="fas fa-wallet text-success"></i>
                                            R$ <?php echo number_format($aprovado['Crédito Pessoal Disponível'], 2, ',', '.'); ?>
                                        </div>
                                        <div class="in-use" title="Em uso">
                                            <i class="fas fa-hand-holding-dollar text-purple"></i>
                                            R$ <?php echo number_format($aprovado['Crédito Pessoal em Uso'], 2, ',', '.'); ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="limit-item">
                                    <span class="limit-label"><strong>Cheque Especial:</strong></span>
                                    <div class="limit-values">
                                        <div class="available" title="Disponível">
                                            <i class="fas fa-wallet text-success"></i>
                                            R$ <?php echo number_format($aprovado['cheque_especial'], 2, ',', '.'); ?>
                                        </div>
                                        <div class="in-use" title="Em uso">
                                            <i class="fas fa-hand-holding-dollar text-purple"></i>
                                            R$ <?php echo number_format($aprovado['cheque_especial_uso'], 2, ',', '.'); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="card-info">
                                <div>
                                    <span class="limit-label">Limite:</span>
                                    <span class="currency">R$ <?php echo number_format($aprovado['limite_cartao'], 2, ',', '.'); ?></span>
                                </div>
                                <div>
                                    <span class="limit-label">Situação:</span>
                                    <span class="status-text"><?php echo htmlspecialchars($aprovado['Situação Conta Cartão']); ?></span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="status <?php echo $aprovado['Débito Automático'] === 'Sim' ? 'status-sim' : 'status-nao'; ?>">
                                <?php echo $aprovado['Débito Automático']; ?>
                            </span>
                        </td>
                        <td class="actions-cell">
                            <div class="actions-wrapper">
                                <button 
                                    onclick="registrarVenda('<?php echo $aprovado['cpf']; ?>', '<?php echo $aprovado['vendas_info']; ?>', '<?php echo $aprovado['Débito Automático']; ?>')"
                                    class="btn btn-primary btn-circle" title="Registrar Venda">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button 
                                    onclick="registrarContato('<?php echo $aprovado['cpf']; ?>')"
                                    class="btn <?php echo $aprovado['contato_id'] ? 'btn-success' : 'btn-secondary'; ?> btn-circle" 
                                    title="<?php echo $aprovado['contato_id'] ? 'Contato realizado em ' . date('d/m/Y H:i', strtotime($aprovado['data_contato'])) . ' por ' . $aprovado['contato_usuario'] : 'Registrar Contato'; ?>">
                                    <i class="fas <?php echo $aprovado['contato_id'] ? 'fa-check' : 'fa-phone'; ?>"></i>
                                </button>
                                <?php
                                if ($aprovado['vendas_info']) {
                                    $vendas = array_map(function($venda) {
                                        list($produto, $valor) = explode(':', $venda);
                                        return ['produto' => $produto, 'valor' => $valor];
                                    }, explode(',', $aprovado['vendas_info']));
                                    
                                    echo '<div class="vendas-info" title="Vendas Registradas">';
                                    foreach ($vendas as $venda) {
                                        $icon = get_product_icon($venda['produto']);
                                        $label = $produtos[$venda['produto']];
                                        $valor = $venda['produto'] === 'DEBITO_AUTOMATICO' ? 'Ativo' : 'R$ ' . number_format($venda['valor'], 2, ',', '.');
                                        echo "<span class='venda-badge' title='{$label}: {$valor}'>";
                                        echo "<i class='fas {$icon}'></i>";
                                        echo "</span>";
                                    }
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="pagination">
        <div class="pagination-info">
            Mostrando <?php echo count($aprovados); ?> de <?php echo $total_records; ?> registros
        </div>
        <div class="pagination-controls">
            <?php if ($total_pages > 1): ?>
                <?php if ($page > 1): ?>
                    <a href="?page=1<?php echo $search ? '&search='.urlencode($search) : ''; ?>" class="page-button">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                    <a href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>" class="page-button">
                        <i class="fas fa-angle-left"></i>
                    </a>
                <?php endif; ?>

                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);

                for ($i = $start_page; $i <= $end_page; $i++):
                ?>
                    <a href="?page=<?php echo $i; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>" class="page-button <?php echo $i === $page ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>" class="page-button">
                        <i class="fas fa-angle-right"></i>
                    </a>
                    <a href="?page=<?php echo $total_pages; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>" class="page-button">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal de Registro de Venda -->
<div id="modal-venda" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Registrar Venda</h3>
            <p class="modal-subtitle">Selecione o produto que deseja vender</p>
        </div>
        <form id="form-venda" method="POST" action="registrar_venda.php">
            <input type="hidden" name="cpf" id="venda-cpf">
            <input type="hidden" name="produtos_vendidos" id="produtos-vendidos">
            <input type="hidden" name="tem_debito" id="tem-debito">
            
            <div class="form-group">
                <label for="produto">Produto:</label>
                <div class="product-options">
                    <?php foreach ($produtos as $key => $value): ?>
                        <label class="product-option" data-produto="<?php echo $key; ?>">
                            <input type="radio" name="produto" value="<?php echo $key; ?>" required onchange="handleProductChange(this)">
                            <span class="product-label">
                                <i class="fas <?php echo get_product_icon($key); ?>"></i>
                                <?php echo $value; ?>
                                <span class="product-status"></span>
                            </span>
                        </label>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="form-group" id="valor-group">
                <label for="valor">Valor:</label>
                <div class="input-currency">
                    <span class="currency-symbol">R$</span>
                    <input type="number" name="valor" id="valor" class="form-control" step="0.01">
                </div>
            </div>

            <div class="form-actions">
                <button type="button" onclick="fecharModal()" class="btn btn-secondary">Cancelar</button>
                <button type="submit" class="btn btn-primary">Confirmar</button>
            </div>
        </form>
    </div>
</div>

<script>
// Remove o evento keyup anterior do campo de busca, pois agora usamos o formulário
document.querySelector('.search-form').addEventListener('submit', function(e) {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput.value.trim()) {
        e.preventDefault();
        window.location.href = window.location.pathname;
    }
});

// Função para lidar com a mudança de produto
function handleProductChange(radio) {
    const valorGroup = document.getElementById('valor-group');
    const valorInput = document.getElementById('valor');
    
    if (radio.value === 'DEBITO_AUTOMATICO') {
        valorGroup.style.display = 'none';
        valorInput.value = '1';
        valorInput.removeAttribute('required');
    } else {
        valorGroup.style.display = 'block';
        valorInput.value = '';
        valorInput.setAttribute('required', 'required');
    }
}

// Função para registrar venda
function registrarVenda(cpf, vendasInfo, temDebito) {
    document.getElementById('venda-cpf').value = cpf;
    document.getElementById('produtos-vendidos').value = vendasInfo;
    document.getElementById('tem-debito').value = temDebito;
    
    const modal = document.getElementById('modal-venda');
    const options = modal.querySelectorAll('.product-option');
    const valorGroup = document.getElementById('valor-group');
    const valorInput = document.getElementById('valor');
    
    // Parse vendas existentes
    const vendas = vendasInfo ? vendasInfo.split(',').reduce((acc, venda) => {
        const [produto, valor] = venda.split(':');
        acc[produto] = valor;
        return acc;
    }, {}) : {};
    
    options.forEach(option => {
        const produto = option.dataset.produto;
        const vendaExistente = vendas[produto];
        const statusSpan = option.querySelector('.product-status');
        const radio = option.querySelector('input[type="radio"]');
        
        // Reseta o estado
        option.classList.remove('disabled', 'has-sale');
        statusSpan.textContent = '';
        radio.checked = false;
        
        // Se for Débito Automático e já tem venda ou está ativo
        if (produto === 'DEBITO_AUTOMATICO' && (vendaExistente || temDebito === 'Sim')) {
            option.classList.add('disabled');
            statusSpan.textContent = '(Já Ativo)';
            radio.disabled = true;
        }
        // Se já tem venda deste produto
        else if (vendaExistente) {
            option.classList.add('has-sale');
            if (produto === 'DEBITO_AUTOMATICO') {
                statusSpan.textContent = '(Ativo)';
            } else {
                statusSpan.textContent = `(R$ ${parseFloat(vendaExistente).toFixed(2)})`;
            }
        }
    });
    
    // Reseta a visibilidade do campo de valor
    valorGroup.style.display = 'block';
    valorInput.value = '';
    valorInput.setAttribute('required', 'required');
    
    modal.classList.add('show');
}

// Formata o campo de valor em tempo real
document.getElementById('valor').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value) {
        value = (parseFloat(value) / 100).toFixed(2);
    }
    e.target.value = value;
});

function fecharModal() {
    const modal = document.getElementById('modal-venda');
    modal.classList.remove('show');
}

// Fecha o modal se clicar fora dele
window.onclick = function(event) {
    const modal = document.getElementById('modal-venda');
    if (event.target == modal) {
        modal.classList.remove('show');
    }
}

// Adiciona a classe selected ao container do radio button selecionado e dispara o evento change
document.querySelectorAll('.product-option').forEach(option => {
    option.addEventListener('click', function() {
        document.querySelectorAll('.product-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        this.classList.add('selected');
        const radio = this.querySelector('input[type="radio"]');
        radio.checked = true;
        // Dispara o evento change manualmente
        radio.dispatchEvent(new Event('change'));
    });
});

// Função para registrar contato
function registrarContato(cpf) {
    fetch('registrar_contato.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'cpf=' + cpf
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Recarrega a página para atualizar o status
            window.location.reload();
        } else {
            alert('Erro ao registrar contato: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao registrar contato');
    });
}
</script>

<style>
/* Estilos adicionais */
.search-box {
    position: relative;
    margin-right: 1rem;
}

.search-form {
    display: flex;
    align-items: center;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--sicoob-gray-500);
    z-index: 1;
}

#searchInput {
    padding-left: 35px;
    height: 38px;
    width: 250px;
    border-radius: 4px;
    border: 1px solid var(--sicoob-gray-300);
}

#searchInput:focus {
    outline: none;
    border-color: var(--sicoob-turquoise);
    box-shadow: 0 0 0 2px rgba(0, 54, 65, 0.1);
}

.modal {
    z-index: 1050;
}

.text-muted {
    color: var(--sicoob-gray-500);
    font-size: 0.875rem;
}

/* Ajuste para o CPF */
td div:first-child {
    font-size: 0.9rem;
}

.text-center {
    text-align: center;
}

.text-success {
    color: var(--sicoob-medium-green);
}

.text-danger {
    color: #dc3545;
}

.text-purple {
    color: var(--sicoob-purple);
}

.limits-info {
    font-size: 0.875rem;
}

.limit-item {
    margin-bottom: 0.5rem;
}

.limit-item:last-child {
    margin-bottom: 0;
}

.limit-label {
    color: var(--sicoob-gray-600);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.25rem;
}

.limit-label strong {
    color: inherit;
}

.limit-values {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.available, .in-use {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.card-info {
    font-size: 0.875rem;
}

.status-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.status-text {
    font-size: 0.875rem;
    color: var(--sicoob-dark-green);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8125rem;
    line-height: 1.4;
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-success {
    background: var(--sicoob-medium-green);
    color: var(--sicoob-white);
}

/* Ajustes na tabela */
.table-responsive {
    overflow-x: auto;
    margin: 0;
    padding: 0;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
}

th {
    background: var(--sicoob-gray-100);
    color: var(--sicoob-dark-green);
    font-weight: 600;
    text-align: left;
    padding: 1rem;
    border-bottom: 2px solid var(--sicoob-gray-200);
    white-space: nowrap;
}

td {
    padding: 1rem;
    border-bottom: 1px solid var(--sicoob-gray-200);
    color: var(--sicoob-gray-800);
    transition: background-color 0.2s ease;
}

tr:hover td {
    background-color: var(--sicoob-gray-100);
}

/* Definição das larguras das colunas */
th:nth-child(1) { width: 12%; } /* CPF */
th:nth-child(2) { width: 20%; } /* Nome */
th:nth-child(3) { width: 5%; }  /* PA */
th:nth-child(4) { width: 10%; } /* Renda */
th:nth-child(5) { width: 20%; } /* Limites */
th:nth-child(6) { width: 12%; } /* Cartão */
th:nth-child(7) { width: 11%; } /* Débito Automático */
th:nth-child(8) { width: 10%; } /* Ações */

/* Tooltip styles */
[title] {
    position: relative;
    cursor: help;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem;
    background: var(--sicoob-dark-green);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10;
    box-shadow: var(--shadow-md);
}

[title]:hover::before {
    content: '';
    position: absolute;
    bottom: calc(100% - 5px);
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: var(--sicoob-dark-green) transparent transparent transparent;
    z-index: 10;
}

/* Ajustes na célula de ações */
.actions-cell {
    padding: 0.75rem;
    vertical-align: middle;
}

.actions-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    min-height: 32px;
}

.btn-circle {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.vendas-info {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.venda-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--sicoob-gray-100);
    color: var(--sicoob-turquoise);
    font-size: 0.875rem;
    cursor: help;
    flex-shrink: 0;
}

.venda-badge:hover {
    background: var(--sicoob-gray-200);
}

.status {
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-block;
    min-width: 70px;
    text-align: center;
    margin: 0 auto;
}

.status-sim {
    background: var(--sicoob-light-green);
    color: var(--sicoob-dark-green);
}

.status-nao {
    background: var(--sicoob-gray-200);
    color: var(--sicoob-gray-600);
}

.header-multiline {
    line-height: 1.2;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    white-space: normal;
}

/* Estilos do Modal */
.modal-subtitle {
    color: var(--sicoob-gray-600);
    margin: 0.5rem 0 1.5rem;
    font-size: 0.875rem;
}

.product-options {
    display: grid;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.product-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.75rem;
    border: 1px solid var(--sicoob-gray-300);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.product-option:hover {
    border-color: var(--sicoob-turquoise);
    background: var(--sicoob-gray-100);
}

.product-option input[type="radio"] {
    display: none;
}

.product-option input[type="radio"]:checked + .product-label {
    color: var(--sicoob-turquoise);
    font-weight: 600;
}

.product-option input[type="radio"]:checked + .product-label i {
    color: var(--sicoob-turquoise);
}

.product-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    color: var(--sicoob-gray-700);
}

.product-label i {
    font-size: 1.125rem;
    color: var(--sicoob-gray-500);
    width: 1.5rem;
    text-align: center;
}

.input-currency {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 0.75rem;
    color: var(--sicoob-gray-600);
}

.input-currency input {
    padding-left: 2.25rem;
}

/* Ajuste no modal */
.modal-content {
    max-width: 400px;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--sicoob-turquoise);
    color: white;
}

.btn-primary:hover {
    background: #009688;
}

.btn-secondary {
    background: var(--sicoob-gray-200);
    color: var(--sicoob-gray-700);
}

.btn-secondary:hover {
    background: var(--sicoob-gray-300);
}

.product-status {
    margin-left: auto;
    font-size: 0.875rem;
    color: var(--sicoob-gray-500);
}

.product-option.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--sicoob-gray-100);
}

.product-option.disabled:hover {
    border-color: var(--sicoob-gray-300);
    background: var(--sicoob-gray-100);
}

.product-option.has-sale {
    border-color: var(--sicoob-turquoise);
    background: rgba(0, 174, 157, 0.05);
}

.product-option.has-sale .product-status {
    color: var(--sicoob-turquoise);
}

tr.contato-realizado {
    background-color: rgba(0, 174, 157, 0.05);
}

tr.contato-realizado:hover td {
    background-color: rgba(0, 174, 157, 0.1);
}

.btn-success {
    background-color: var(--sicoob-medium-green);
    color: white;
}

.btn-success:hover {
    background-color: var(--sicoob-dark-green);
}
</style>