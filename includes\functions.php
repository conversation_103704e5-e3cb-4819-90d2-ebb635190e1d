<?php
// Função para mascarar CPF
function mask_cpf($cpf) {
    return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $cpf);
}

// Função para normalizar o número do PA para dois dígitos
function normalize_pa($pa) {
    return str_pad($pa, 2, '0', STR_PAD_LEFT);
}

// Função para retornar o ícone adequado para cada produto
function get_product_icon($produto) {
    switch ($produto) {
        case 'CREDITO_PESSOAL':
            return 'fa-money-bill-wave';
        case 'CHEQUE_ESPECIAL':
            return 'fa-money-check';
        case 'CARTAO_CREDITO':
            return 'fa-credit-card';
        case 'DEBITO_AUTOMATICO':
            return 'fa-sync';
        default:
            return 'fa-tag';
    }
}

/**
 * Retorna o desafio ativo no momento
 * @return array|false Retorna o desafio ativo ou false se não houver
 */
function get_desafio_ativo() {
    global $pdo;
    
    try {
        // Define o fuso horário
        date_default_timezone_set('America/Sao_Paulo');
        
        // Log inicial
        $now = new DateTime();
        error_log("=== Início da verificação de desafio ativo ===");
        error_log("Data/Hora atual: " . $now->format('Y-m-d H:i:s'));
        
        // Primeiro, vamos verificar todos os desafios ativos
        $stmt = $pdo->query("
            SELECT id, nome, status, data_inicio, data_fim, horario_fim 
            FROM desafios 
            WHERE status = 'ativo'
        ");
        $desafios = $stmt->fetchAll();
        error_log("Desafios com status 'ativo': " . count($desafios));
        foreach ($desafios as $d) {
            error_log("Desafio ID {$d['id']}: nome={$d['nome']}, início={$d['data_inicio']}, fim={$d['data_fim']}, horário_fim={$d['horario_fim']}");
        }
        
        // Agora a consulta principal - Usando diretamente as datas sem STR_TO_DATE
        $stmt = $pdo->prepare("
            SELECT * FROM desafios 
            WHERE status = 'ativo' 
            AND data_inicio <= CURDATE()
            AND (
                data_fim > CURDATE()
                OR (
                    data_fim = CURDATE()
                    AND (
                        horario_fim IS NULL 
                        OR TIME(horario_fim) > CURRENT_TIME()
                    )
                )
            )
            LIMIT 1
        ");
        $stmt->execute();
        $desafio = $stmt->fetch();
        
        if ($desafio) {
            error_log("Desafio ativo encontrado:");
            error_log("ID: {$desafio['id']}");
            error_log("Nome: {$desafio['nome']}");
            error_log("Data Início: {$desafio['data_inicio']}");
            error_log("Data Fim: {$desafio['data_fim']}");
            error_log("Horário Fim: {$desafio['horario_fim']}");
        } else {
            error_log("Nenhum desafio ativo encontrado");
        }
        
        error_log("=== Fim da verificação de desafio ativo ===");
        return $desafio;
    } catch (Exception $e) {
        error_log("Erro ao verificar desafio ativo: " . $e->getMessage());
        return false;
    }
}

/**
 * Verifica se o sistema está aceitando vendas no momento
 * @return bool
 */
function is_sistema_ativo() {
    global $pdo;
    
    try {
        // Define o fuso horário
        date_default_timezone_set('America/Sao_Paulo');
        
        // Log inicial
        $now = new DateTime();
        error_log("=== Início da verificação do sistema ativo ===");
        error_log("Data/Hora atual: " . $now->format('Y-m-d H:i:s'));
        
        // Primeiro, vamos verificar todos os desafios ativos
        $stmt = $pdo->query("
            SELECT id, nome, status, data_inicio, data_fim, horario_fim 
            FROM desafios 
            WHERE status = 'ativo'
        ");
        $desafios = $stmt->fetchAll();
        error_log("Desafios com status 'ativo': " . count($desafios));
        foreach ($desafios as $d) {
            error_log("Desafio ID {$d['id']}: nome={$d['nome']}, início={$d['data_inicio']}, fim={$d['data_fim']}, horário_fim={$d['horario_fim']}");
        }
        
        // Agora a consulta principal - Usando diretamente as datas sem STR_TO_DATE
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total
            FROM desafios 
            WHERE status = 'ativo' 
            AND data_inicio <= CURDATE()
            AND (
                data_fim > CURDATE()
                OR (
                    data_fim = CURDATE()
                    AND (
                        horario_fim IS NULL 
                        OR TIME(horario_fim) > CURRENT_TIME()
                    )
                )
            )
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        $ativo = $result['total'] > 0;
        
        error_log("Resultado da verificação: " . ($ativo ? "ATIVO" : "INATIVO"));
        error_log("=== Fim da verificação do sistema ativo ===");
        
        return $ativo;
    } catch (Exception $e) {
        error_log("Erro ao verificar sistema ativo: " . $e->getMessage());
        return false;
    }
} 