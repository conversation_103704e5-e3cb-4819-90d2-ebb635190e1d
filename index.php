<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Verifica autenticação
check_auth();

// Obtém o PA do usuário logado
$pa = get_user_pa();
$is_admin = $_SESSION['is_admin'];
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Consulta - Sicoob Credilivre</title>
    <link rel="icon" type="image/png" href="assets/images/icon.png">
    <link rel="stylesheet" href="assets/css/style.css">
    <!-- Fonte Sicoob -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="container">
            <img src="assets/images/logo1.png" alt="Sicoob" class="logo">
            <?php include 'includes/menu.php'; ?>
        </div>
    </header>

    <main class="container">
        <div class="page-header">
            <h1>
                <i class="fas fa-list"></i>
                Consulta de Associados
            </h1>
            <p class="page-subtitle">Sistema de consulta aos cadastros aprovados</p>
        </div>
        <div class="content">
            <!-- O conteúdo da listagem será carregado aqui -->
            <?php include 'includes/lista_aprovados.php'; ?>
        </div>
    </main>

    <style>
    .page-header {
        text-align: center;
        margin: 2rem 0 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, var(--sicoob-turquoise) 0%, var(--sicoob-dark-green) 100%);
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 54, 65, 0.15);
        color: white;
    }

    .page-header h1 {
        margin: 0 0 0.5rem 0;
        font-size: 2.5rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .page-header h1 i {
        color: var(--sicoob-light-green);
    }

    .page-subtitle {
        margin: 0;
        font-size: 1.2rem;
        opacity: 0.9;
        font-weight: 400;
    }

    @media (max-width: 768px) {
        .page-header {
            padding: 1.5rem;
            margin: 1.5rem 0 2rem;
        }

        .page-header h1 {
            font-size: 2rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .page-subtitle {
            font-size: 1rem;
        }
    }
    </style>

    <script src="assets/js/main.js"></script>
</body>
</html> 