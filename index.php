<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Verifica autenticação
check_auth();

// Obtém o PA do usuário logado
$pa = get_user_pa();
$is_admin = $_SESSION['is_admin'];
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Consulta - Sicoob Credilivre</title>
    <link rel="icon" type="image/png" href="assets/images/icon.png">
    <link rel="stylesheet" href="assets/css/style.css">
    <!-- Fonte Sicoob -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- FontAwesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <img src="assets/images/logo1.png" alt="Sicoob" class="logo">
            <?php include 'includes/menu.php'; ?>
        </div>
    </header>

    <main class="container">
        <div class="campaign-header">
            <h1>
                <span class="campaign-title">
                    <i class="fas fa-robot"></i>
                    ATUALIZAÇÃO CADASTRAL - RPA
                    <i class="fas fa-cogs"></i>
                </span>
                <span class="campaign-subtitle">
                    <i class="fas fa-sync"></i>
                    consulta aos cadastros atualizados via RPA
                    <i class="fas fa-chart-line"></i>
                </span>
            </h1>
        </div>
        <div class="content">
            <!-- O conteúdo da listagem será carregado aqui -->
            <?php include 'includes/lista_aprovados.php'; ?>
        </div>
    </main>

    <style>
    .campaign-header {
        text-align: center;
        margin: 2rem 0 3rem;
        padding: 2.5rem;
        background: linear-gradient(135deg, var(--sicoob-turquoise) 0%, var(--sicoob-dark-green) 100%);
        border-radius: 8px;
        box-shadow: 0 8px 16px rgba(0, 54, 65, 0.15);
        position: relative;
        overflow: hidden;
    }

    .campaign-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--sicoob-light-green);
    }

    .campaign-header h1 {
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        position: relative;
    }

    .campaign-title {
        color: var(--sicoob-light-green);
        font-size: 3rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 2px;
        line-height: 1.2;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .campaign-title i {
        font-size: 2.5rem;
        animation: pulse 2s infinite;
    }

    .campaign-subtitle {
        color: white;
        font-size: 1.5rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .campaign-subtitle i {
        font-size: 1.25rem;
        opacity: 0.9;
    }

    .campaign-subtitle i.fa-sync {
        animation: spin 4s linear infinite;
    }

    .campaign-subtitle i.fa-chart-line {
        animation: slideUp 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    @keyframes slideUp {
        0% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
        100% { transform: translateY(0); }
    }

    @media (max-width: 768px) {
        .campaign-header {
            padding: 2rem 1.5rem;
            margin: 1.5rem 0 2rem;
        }

        .campaign-title {
            font-size: 2rem;
            letter-spacing: 1px;
        }

        .campaign-title i {
            font-size: 1.75rem;
        }

        .campaign-subtitle {
            font-size: 1.125rem;
        }

        .campaign-subtitle i {
            font-size: 1rem;
        }
    }
    </style>

    <script src="assets/js/main.js"></script>
</body>
</html> 