<?php
require_once '../config/config.php';

try {
    // Define o fuso horário
    date_default_timezone_set('America/Sao_Paulo');
    
    // Inicia a transação
    $pdo->beginTransaction();

    // Busca desafios ativos que já passaram da data final
    $hoje = new DateTime();
    $hoje->setTime(0, 0, 0); // Início do dia atual
    
    $stmt = $pdo->prepare("
        UPDATE desafios 
        SET status = 'finalizado' 
        WHERE status = 'ativo' 
        AND DATE(data_fim) < ?
    ");
    
    $stmt->execute([$hoje->format('Y-m-d')]);
    
    $desafios_finalizados = $stmt->rowCount();
    
    // Commit da transação
    $pdo->commit();
    
    // Log do resultado
    $log_message = date('Y-m-d H:i:s') . " - {$desafios_finalizados} desafio(s) finalizado(s)\n";
    file_put_contents(__DIR__ . '/logs/finalizacao_desafios.log', $log_message, FILE_APPEND);
    
    echo "Processo concluído. {$desafios_finalizados} desafio(s) finalizado(s).";

} catch (Exception $e) {
    // Em caso de erro, faz rollback
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // Log do erro
    $error_message = date('Y-m-d H:i:s') . " - Erro: " . $e->getMessage() . "\n";
    file_put_contents(__DIR__ . '/logs/finalizacao_desafios.log', $error_message, FILE_APPEND);
    
    echo "Erro ao finalizar desafios: " . $e->getMessage();
} 